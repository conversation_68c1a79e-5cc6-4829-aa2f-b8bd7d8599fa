const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const logging = require('../common/logging')
const { 
  getAllFoodNamesQuery,
  getFoodNamesBySearchQuery,
  getServingUnitByFoodNameQuery
} = require('../queries/nutrition-query')

const nutritionContainer = 'LifeStyleFoodList'

class NutritionRepository {
  
  async getAllFoodNames() {
    try {
      const query = getAllFoodNamesQuery()
      const result = await cosmosDbContext.queryItems(query, nutritionContainer)
      return result
    } catch (error) {
      logging.logError('Failed to fetch all food names from database', error)
      throw new Error('Failed to fetch all food names from database')
    }
  }

  async getFoodNamesBySearch(searchTerm) {
    try {
      const query = getFoodNamesBySearchQuery(searchTerm)
      const result = await cosmosDbContext.queryItems(query, nutritionContainer)
      return result
    } catch (error) {
      logging.logError(`Failed to search food names with term: ${searchTerm}`, error)
      throw new Error('Failed to search food names from database')
    }
  }

  async getServingUnitByFoodName(foodName) {
    try {
      const query = getServingUnitByFoodNameQuery(foodName)
      const result = await cosmosDbContext.queryItems(query, nutritionContainer)
      return result && result.length > 0 ? result[0] : null
    } catch (error) {
      logging.logError(`Failed to fetch serving unit for food: ${foodName}`, error)
      throw new Error('Failed to fetch serving unit from database')
    }
  }
}

module.exports = new NutritionRepository()
