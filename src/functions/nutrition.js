const { app } = require('@azure/functions');
const nutritionHandler = require('../handlers/nutrition-handler');
const { HttpStatusCode } = require('axios');
const { jsonResponse } = require('../common/helper');
const { HttpMethod } = require('../common/constant');

app.http('food-list', {
    methods: ['GET'],
    route: 'food/list',
    authLevel: 'function',
    handler: async (req, context) => {
        context.log(`Http function processed request for url "${req.url}"`);
        
        switch (req.method) {
            case HttpMethod.get:
                try {
                    const input = req.query.get('input') || req.query.get('q');
                    const data = await nutritionHandler.getFoodNames(input);
                    return jsonResponse(data);
                } catch (err) {
                    context.log('Error fetching food list:', err);
                    return jsonResponse('Error fetching food list', HttpStatusCode.InternalServerError);
                }
                
            default:
                return jsonResponse(`Unsupported HTTP method`, HttpStatusCode.MethodNotAllowed);
        }
    }
});

app.http('food-serving-unit', {
    methods: ['GET'],
    route: 'food/serving-unit',
    authLevel: 'function',
    handler: async (req, context) => {
        context.log(`Http function processed request for url "${req.url}"`);
        
        switch (req.method) {
            case HttpMethod.get:
                try {
                    const foodName = req.query.get('foodName') || req.query.get('name');
                    
                    if (!foodName || foodName.trim().length === 0) {
                        return jsonResponse({});
                    }

                    const data = await nutritionHandler.getServingUnitByFoodName(foodName.trim());
                    return jsonResponse(data);
                } catch (err) {
                    context.log('Error fetching serving unit:', err);
                    return jsonResponse('Error fetching serving unit', HttpStatusCode.InternalServerError);
                }
                
            default:
                return jsonResponse(`Unsupported HTTP method`, HttpStatusCode.MethodNotAllowed);
        }
    }
});
