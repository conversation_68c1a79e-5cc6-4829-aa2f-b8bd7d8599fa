const logging = require('../common/logging')
const nutritionRepository = require('../repositories/nutrition-repository')

class NutritionService {

    async getFoodNames(searchTerm = '') {
        try {
            let result
            if (searchTerm && searchTerm.trim().length > 0) {
                result = await nutritionRepository.getFoodNamesBySearch(searchTerm.trim())
            } else {
                result = await nutritionRepository.getAllFoodNames()
            }
            return result
        } catch (error) {
            logging.logError("unable to get food names", error)
            return null
        }
    }

    async getServingUnitByFoodName(foodName) {
        try {
            var result = await nutritionRepository.getServingUnitByFoodName(foodName)
            return result
        } catch (error) {
            logging.logError("unable to get serving unit for food", error)
            return null
        }
    }

}

module.exports = new NutritionService()

