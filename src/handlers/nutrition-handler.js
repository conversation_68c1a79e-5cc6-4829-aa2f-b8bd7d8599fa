const nutritionService = require('../services/nutrition-service');
const { logError, logInfo } = require('../common/logging');

class NutritionHandler {

    async getFoodNames(searchTerm = '') {
        try {
            if (searchTerm && searchTerm.trim().length > 0) {
                logInfo(`Get food list starting with: ${searchTerm}`);
            } else {
                logInfo(`Get all food list`);
            }
            var result = await nutritionService.getFoodNames(searchTerm);
            return result;
        } catch (error) {
            logError(error);
            return null;
        }
    }

    async getServingUnitByFoodName(foodName) {
        try {
            logInfo(`Get serving unit for food: ${foodName}`);
            var result = await nutritionService.getServingUnitByFoodName(foodName);
            return result;
        } catch (error) {
            logError(error);
            return null;
        }
    }

}

module.exports = new NutritionHandler();
