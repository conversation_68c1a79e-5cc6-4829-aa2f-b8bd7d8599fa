module.exports = {
  getAllFoodNamesQuery: () => {
    return "SELECT c.food_name FROM c ORDER BY c.food_name ASC"
  },

  getFoodNamesBySearchQuery: (searchTerm) => {
    return `SELECT c.food_name FROM c WHERE STARTSWITH(UPPER(c.food_name), UPPER('${searchTerm}')) ORDER BY c.food_name ASC`
  },

  getServingUnitByFoodNameQuery: (foodName) => {
    return `SELECT c.servings_unit FROM c WHERE UPPER(c.food_name) = UPPER('${foodName}')`
  }
}
