Appointment
-----------
{
    "id": "APM20240725624",
    "doctorId": "7066645a-6c13-4443-8b71-1738fd55bed3",
    "date": "2024-07-25",
    "created_by": "7066645a-6c13-4443-8b71-1738fd55bed3",
    "updated_by": "7066645a-6c13-4443-8b71-1738fd55bed3",
    "created_on": "2024-07-25T12:16:38.672Z",
    "updated_on": "2025-01-22T16:37:10.977Z",
    "_rid": "gG1xAOAq1NAXAAAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xAOAq1NA=/docs/gG1xAOAq1NAXAAAAAAAAAA==/",
    "_etag": "\"9a035de5-0000-0200-0000-67911eb60000\"",
    "_attachments": "attachments/",
    "department": "inPatient",
    "_ts": **********
}

CustomiseEmrs
---------------
{
    "source_name": "miscellaneous_clinical_metrics",
    "data": [
        {
            "key": "Pain Scale Score (0-10)",
            "value": "Pain Scale Score (0-10)",
            "abbreviationCode": "PSS",
            "unit": "score"
        },
        {
            "key": "Fatigue Severity Score (FSS)",
            "value": "Fatigue Severity Score (FSS)",
            "abbreviationCode": "FSS",
            "unit": "score"
        },
        {
            "key": "Physical Activity Level (METs)",
            "value": "Physical Activity Level (METs)",
            "abbreviationCode": "PAL",
            "unit": "METs"
        },
        {
            "key": "Sleep Efficiency (%)",
            "value": "Sleep Efficiency (%)",
            "abbreviationCode": "SE",
            "unit": "%"
        },
        {
            "key": "Stress Level (HRV-based or Cortisol-based)",
            "value": "Stress Level (HRV-based or Cortisol-based)",
            "abbreviationCode": "Stress",
            "unit": "varies"
        },
        {
            "key": "Gait Speed (m/s)",
            "value": "Gait Speed (m/s)",
            "abbreviationCode": "GS",
            "unit": "m/s"
        },
        {
            "key": "Hand Grip Strength (kg)",
            "value": "Hand Grip Strength (kg)",
            "abbreviationCode": "HGS",
            "unit": "kg"
        }
    ],
    "created_by": "local_debugging",
    "updated_by": "local_debugging",
    "created_on": "2025-02-23T07:48:58.355Z",
    "updated_on": "2025-02-23T07:48:58.355Z",
    "id": "5ccd8412-6909-48a6-9f93-6cd43331eba0",
    "_rid": "gG1xAJwiwswNAAAAAAAAAA==",
    
    DoctorCustomiseEmrs
    -----------------------
    {
    "doc_assist_preference": "",
    "preferred_language_for_ambient_listening": "English",
    "selected_tiles": [],
    "tile_layout": "",
    "medical_note_summary_template": "",
    "extraNote1": "Summary",
    "extraNote2": "Medication",
    "extraNote3": "Notes",
    "letterHeadDetails": "MBBS \nMD in Surgery\n33 years experience",
    "organizationLogo": "https://ermdevstoragedata.blob.core.windows.net/file-uploads/user/6a3e1c49-bcfc-4ef9-ae04-bc68c2d3f753/document/logo.jpg",
    "digitalSignature": "https://ermdevstoragedata.blob.core.windows.net/file-uploads/user/6a3e1c49-bcfc-4ef9-ae04-bc68c2d3f753/document/sign.png",
    "doctorId": "6a3e1c49-bcfc-4ef9-ae04-bc68c2d3f753",
    "id": "883a9250-9055-4397-9afc-6dfc0798730c",
    "created_by": "45f8ba73-e782-4152-b23d-26f41c372016",
    "updated_by": "45f8ba73-e782-4152-b23d-26f41c372016",
    "created_on": "2025-08-07T02:04:21.483Z",
    "updated_on": "2025-08-18T04:57:25.118Z",
    "_rid": "gG1xAPV6iYeDAAAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xAPV6iYc=/docs/gG1xAPV6iYeDAAAAAAAAAA==/",
    "_etag": "\"440159c9-0000-0200-0000-68a2b2b50000\"",
    "_attachments": "attachments/",
    "_ts": 1755493045
}
Doctors
----------
{
    "id": "a1fdf239-109d-481d-9148-aa4e1fe38905",
    "username": "b50a3ae3c54d68782b7ef557b2a9f396ea24ef6b75919299f71b2b80755c1f5e",
    "general": {
        "fullName": "7a3abf163b78ef1d33e8bfb3a5d8c2fd",
        "designation": "",
        "department": "",
        "doctorID": "",
        "contactNumber": "",
        "workEmail": ""
    },
    "personal": {
        "age": null,
        "bloodGroup": "",
        "height": null,
        "weight": null,
        "isPersonWithDisability": false,
        "percentOfDisability": null,
        "identificationMark": "",
        "maritalStatus": "",
        "dateOfWedding": "",
        "nationality": "",
        "religion": "",
        "caste": "",
        "category": "",
        "reservationDetails": "",
        "idProof": {
            "type": "",
            "number": "",
            "description": "",
            "url": ""
        },
        "hometownDetails": {
            "hometown": "",
            "state": "",
            "district": "",
            "country": ""
        },
        "birthDetails": {
            "placeOfBirth": "",
            "state": "",
            "district": "",
            "country": ""
        },
        "address": {
            "permanent": {
                "home": "",
                "street": "",
                "city": "",
                "pinCode": "",
                "district": "",
                "state": "",
                "country": "",
                "phone": "",
                "mobile": "",
                "email": "",
                "proof": {
                    "description": "",
                    "url": ""
                }
            },
            "current": {
                "home": "",
                "street": "",
                "city": "",
                "pinCode": "",
                "district": "",
                "state": "",
                "country": "",
                "phone": "",
                "mobile": "",
                "email": "",
                "proof": {
                    "description": "",
                    "url": ""
                }
            }
        }
    },
    "emergencyContacts": [],
    "professionalDetails": {
        "medicalRegistration": {
            "councilName": "",
            "registrationNumber": "",
            "validFrom": "",
            "validTo": "",
            "proof": {
                "description": "",
                "url": ""
            }
        },
        "specialties": [],
        "qualifications": [],
        "certifications": [],
        "experience": []
    },
    "family": [],
    "languagesKnown": [],
    "bankDetails": [],
    "insurance": [],
    "researchAndPublications": [],
    "affiliations": [],
    "documents": {},
    "_rid": "gG1xAJ17ZE7kAAAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xAJ17ZE4=/docs/gG1xAJ17ZE7kAAAAAAAAAA==/",
    "_etag": "\"12008aa8-0000-0200-0000-689effbf0000\"",
    "_attachments": "attachments/",
    "created_by": "130c5dca-c53e-4b25-b155-dd0fde0075dd",
    "updated_by": "130c5dca-c53e-4b25-b155-dd0fde0075dd",
    "created_on": "2025-08-15T09:37:03.674Z",
    "updated_on": "2025-08-15T09:37:03.674Z",
    "consultationFee": 0,
    "_ts": **********
}

lab_tests
-----------
{
    "_rid": "gG1xALj3ID3qmAEAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xALj3ID0=/docs/gG1xALj3ID3qmAEAAAAAAA==/",
    "_etag": "\"020055dc-0000-0200-0000-683804f20000\"",
    "_attachments": "attachments/",
    "created_by": "",
    "updated_by": "",
    "created_on": "2025-05-29T06:55:46.137Z",
    "updated_on": "2025-05-29T06:55:46.137Z",
    "LOINC_NUM": "9999-4",
    "COMPONENT": "R wave duration.lead AVL",
    "PROPERTY": "Time",
    "TIME_ASPCT": "Pt",
    "SYSTEM": "Heart",
    "SCALE_TYP": "Qn",
    "METHOD_TYP": "EKG",
    "CLASS": "EKG.MEAS",
    "VersionLastChanged": 2.48,
    "CHNG_TYPE": "MIN",
    "DefinitionDescription": "",
    "STATUS": "ACTIVE",
    "CONSUMER_NAME": "",
    "CLASSTYPE": 2,
    "FORMULA": "",
    "EXMPL_ANSWERS": "",
    "SURVEY_QUEST_TEXT": "",
    "SURVEY_QUEST_SRC": "",
    "UNITSREQUIRED": "Y",
    "RELATEDNAMES2": "Cardiac; Cardio; Cardiology; Durat; ECG; EKG.MEASUREMENTS; Electrocardiogram; Electrocardiograph; Heart Disease; Hrt; Painter's colic; PB; Plumbism; Point in time; QNT; Quan; Quant; Quantitative; R prime; R' wave dur L-AVL; R wave dur L-AVL; Random; Right",
    "SHORTNAME": "R wave dur L-AVL",
    "ORDER_OBS": "Observation",
    "HL7_FIELD_SUBFIELD_ID": "",
    "EXTERNAL_COPYRIGHT_NOTICE": "",
    "EXAMPLE_UNITS": "ms",
    "LONG_COMMON_NAME": "R wave duration in lead AVL",
    "EXAMPLE_UCUM_UNITS": "ms",
    "STATUS_REASON": "",
    "STATUS_TEXT": "",
    "CHANGE_REASON_PUBLIC": "",
    "COMMON_TEST_RANK": "",
    "COMMON_ORDER_RANK": "",
    "HL7_ATTACHMENT_STRUCTURE": "",
    "EXTERNAL_COPYRIGHT_LINK": "",
    "PanelType": "",
    "AskAtOrderEntry": "",
    "AssociatedObservations": "",
    "VersionFirstReleased": "1.0i",
    "ValidHL7AttachmentRequest": "",
    "DisplayName": "",
    "id": "9999-4",
    "_ts": **********
}

lab_reports-meta-data

--------------
{
    "id": "64dbab04-7f55-44ff-9435-533279263b1e",
    "fileName": "lab_results.pdf",
    "fileSize": 2058,
    "blobPath": "patients/**********/labtest/9e951ca4-70b5-407e-baa9-908d3235b56f/64dbab04-7f55-44ff-9435-533279263b1e-lab_results.pdf",
    "encryptionKey": "98bb0b44d5a1a5d6393132601303941646132b80e665354ce0a2c9d1c6d037aa",
    "iv": "eaff7f4d6467a3c8676c90efe8db33f1",
    "patientId": "**********",
    "labTestId": "9e951ca4-70b5-407e-baa9-908d3235b56f",
    "ocrStatus": "completed",
    "detectedLanguage": null,
    "ocrData": {
        "raw_ocr": [
            {
                "page_number": 1,
                "blocks": [
                    {
                        "type": "paragraph",
                        "content": "Test Name Result Unit Reference Interval"
                    },
                    {
                        "type": "key_value",
                        "key": "B lymphocytes (Bld) 168.0 #/Vol Normal",
                        "value": "<150"
                    },
                    {
                        "type": "key_value",
                        "key": "R wave dur L-V2 145.0 ms Normal",
                        "value": "<200"
                    },
                    {
                        "type": "key_value",
                        "key": "Oxygen content (Mid right atrium) 190.0 Moles/Vol Normal",
                        "value": "<150"
                    },
                    {
                        "type": "key_value",
                        "key": "Fasciola sp Ab (S) 159.0 Titer Desirable",
                        "value": "<600"
                    }
                ]
            }
        ],
        "flattened": "[TEST] Test Name Result Unit Reference Interval\n[FIELD] B lymphocytes (Bld) 168.0 #/Vol Normal: <150\n[FIELD] R wave dur L-V2 145.0 ms Normal: <200\n[FIELD] Oxygen content (Mid right atrium) 190.0 Moles/Vol Normal: <150\n[FIELD] Fasciola sp Ab (S) 159.0 Titer Desirable: <600",
        "structured": {
            "patient": {
                "name": null,
                "sex": null,
                "age": null,
                "date_of_birth": null
            },
            "doctor": {
                "name": null
            },
            "date_of_test": null,
            "lab_or_facility": "Not specified",
            "test_results": [
                {
                    "test_name": "B lymphocytes (Bld)",
                    "value": "168.0",
                    "unit": "#/Vol",
                    "reference_interval": "<150"
                },
                {
                    "test_name": "R wave dur L-V2",
                    "value": "145.0",
                    "unit": "ms",
                    "reference_interval": "<200"
                },
                {
                    "test_name": "Oxygen content (Mid right atrium)",
                    "value": "190.0",
                    "unit": "Moles/Vol",
                    "reference_interval": "<150"
                },
                {
                    "test_name": "Fasciola sp Ab (S)",
                    "value": "159.0",
                    "unit": "Titer",
                    "reference_interval": "<600"
                }
            ]
        }
    },
    "fileType": "application/pdf",
    "uploadedAt": "2025-08-14T07:19:00.414Z",
    "_rid": "gG1xAPwQQcKXAAAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xAPwQQcI=/docs/gG1xAPwQQcKXAAAAAAAAAA==/",
    "_etag": "\"4c00a954-0000-0200-0000-689d8e060000\"",
    "_attachments": "attachments/",
    "_ts": 1755155974
}

LifeStyleFoodList
-----------------
{
    "food_code": "ASC001",
    "food_name": "Hot tea (Garam Chai)",
    "primarysource": "asc_manual",
    "energy_kj": 68.16,
    "energy_kcal": 16.14,
    "carb_g": 2.58,
    "protein_g": 0.39,
    "fat_g": 0.53,
    "freesugar_g": 2.58,
    "fibre_g": 0,
    "sfa_mg": 321.5,
    "mufa_mg": 144.18,
    "pufa_mg": 16.39,
    "cholesterol_mg": 0,
    "calcium_mg": 14.2,
    "phosphorus_mg": 11.5,
    "magnesium_mg": 1.04,
    "sodium_mg": 3.12,
    "potassium_mg": 13.95,
    "iron_mg": 0.02,
    "copper_mg": 0.01,
    "selenium_ug": 0.11,
    "chromium_mg": 0,
    "manganese_mg": 0,
    "molybdenum_mg": 0,
    "zinc_mg": 0.04,
    "vita_ug": 0,
    "vite_mg": 0.03,
    "vitd2_ug": 0,
    "vitd3_ug": 0,
    "vitk1_ug": 0,
    "vitk2_ug": 0,
    "folate_ug": 0.86,
    "vitb1_mg": 0,
    "vitb2_mg": 0.01,
    "vitb3_mg": 0.01,
    "vitb5_mg": 0.04,
    "vitb6_mg": 0,
    "vitb7_ug": 0.24,
    "vitb9_ug": 0.86,
    "vitc_mg": 0.24,
    "carotenoids_ug": 23.75,
    "servings_unit": "tea cup",
    "unit_serving_energy_kj": 143.48,
    "unit_serving_energy_kcal": 33.98,
    "unit_serving_carb_g": 5.43,
    "unit_serving_protein_g": 0.82,
    "unit_serving_fat_g": 1.12,
    "unit_serving_freesugar_g": 5.42,
    "unit_serving_fibre_g": 0,
    "unit_serving_sfa_mg": 676.75,
    "unit_serving_mufa_mg": 303.5,
    "unit_serving_pufa_mg": 34.5,
    "unit_serving_cholesterol_mg": 0,
    "unit_serving_calcium_mg": 29.9,
    "unit_serving_phosphorus_mg": 24.21,
    "unit_serving_magnesium_mg": 2.18,
    "unit_serving_sodium_mg": 6.56,
    "unit_serving_potassium_mg": 29.35,
    "unit_serving_iron_mg": 0.05,
    "unit_serving_copper_mg": 0.01,
    "unit_serving_selenium_ug": 0.24,
    "unit_serving_chromium_mg": 0,
    "unit_serving_manganese_mg": 0,
    "unit_serving_molybdenum_mg": 0,
    "unit_serving_zinc_mg": 0.09,
    "unit_serving_vita_ug": 0,
    "unit_serving_vite_mg": 0.05,
    "unit_serving_vitd2_ug": 0,
    "unit_serving_vitd3_ug": 0,
    "unit_serving_vitk1_ug": 0,
    "unit_serving_vitk2_ug": 0,
    "unit_serving_folate_ug": 1.8,
    "unit_serving_vitb1_mg": 0.01,
    "unit_serving_vitb2_mg": 0.03,
    "unit_serving_vitb3_mg": 0.02,
    "unit_serving_vitb5_mg": 0.09,
    "unit_serving_vitb6_mg": 0.01,
    "unit_serving_vitb7_ug": 0.51,
    "unit_serving_vitb9_ug": 1.8,
    "unit_serving_vitc_mg": 0.5,
    "unit_serving_carotenoids_ug": 50,
    "id": "ASC001",
    "_rid": "gG1xANQUdssBAAAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xANQUdss=/docs/gG1xANQUdssBAAAAAAAAAA==/",
    "_etag": "\"21004435-0000-0200-0000-689c4a1d0000\"",
    "_attachments": "attachments/",
    "_ts": 1755073053
}

LifeStyles
----------------------
{
    "0": {
        "source": "physical_activity_attitude",
        "questions": [
            {
                "id": "attitude",
                "title": "Attitude",
                "icon": "attitude",
                "fields": [
                    {
                        "id": "exercise_preference",
                        "label": "Do you like doing exercise?",
                        "type": "radio",
                        "options": [
                            "Yes",
                            "No"
                        ],
                        "required": true
                    },
                    {
                        "id": "reason_for_choice",
                        "label": "Reason for your choice",
                        "type": "section",
                        "fields": [
                            {
                                "id": "knowledge_of_exercise",
                                "label": "Knowledge of exercise",
                                "type": "radio",
                                "options": [
                                    "It is good for my health",
                                    "I am not sure about the health benefits"
                                ]
                            },
                            {
                                "id": "availability_of_gyms",
                                "label": "Availability of Gyms",
                                "type": "radio",
                                "options": [
                                    "It is available in my area",
                                    "It is not available in my area"
                                ]
                            },
                            {
                                "id": "affordability_of_gym",
                                "label": "Affordability of Gym",
                                "type": "radio",
                                "options": [
                                    "It is within my budget",
                                    "It is not within my budget"
                                ]
                            },
                            {
                                "id": "subjective_feeling",
                                "label": "Subjective Feeling",
                                "type": "radio",
                                "options": [
                                    "I feel better",
                                    "I don't feel better"
                                ]
                            },
                            {
                                "id": "clinical_improvement",
                                "label": "Clinical Improvement",
                                "type": "radio",
                                "options": [
                                    "My health parameters are improving",
                                    "My health parameters are not improving"
                                ]
                            },
                            {
                                "id": "social_support",
                                "label": "Social Support",
                                "type": "radio",
                                "options": [
                                    "I have a strong social support",
                                    "I do not have a strong social support"
                                ]
                            }
                        ]
                    },
                    {
                        "id": "physical_changes",
                        "label": "Rank the top 3 physical changes you want to make",
                        "type": "section",
                        "fields": [
                            {
                                "id": "physical_change_1",
                                "label": "Physical Change 1",
                                "type": "textarea",
                                "placeholder": "Lorem ipsum dolor sit amet, c"
                            },
                            {
                                "id": "importance_1",
                                "label": "How important is it for you to make the change?",
                                "type": "slider",
                                "min": 0,
                                "max": 10,
                                "step": 1,
                                "defaultValue": 1
                            },
                            {
                                "id": "confidence_1",
                                "label": "How confident are you to make the change?",
                                "type": "slider",
                                "min": 0,
                                "max": 10,
                                "step": 1,
                                "defaultValue": 1
                            },
                            {
                                "id": "physical_change_2",
                                "label": "Physical Change 2",
                                "type": "textarea",
                                "placeholder": "Lorem ipsum dolor sit amet, c"
                            },
                            {
                                "id": "importance_2",
                                "label": "How important is it for you to make the change?",
                                "type": "slider",
                                "min": 0,
                                "max": 10,
                                "step": 1,
                                "defaultValue": 1
                            },
                            {
                                "id": "confidence_2",
                                "label": "How confident are you to make the change?",
                                "type": "slider",
                                "min": 0,
                                "max": 10,
                                "step": 1,
                                "defaultValue": 1
                            },
                            {
                                "id": "physical_change_3",
                                "label": "Physical Change 3",
                                "type": "textarea",
                                "placeholder": "Lorem ipsum dolor sit amet, c"
                            },
                            {
                                "id": "importance_3",
                                "label": "How important is it for you to make the change?",
                                "type": "slider",
                                "min": 0,
                                "max": 10,
                                "step": 1,
                                "defaultValue": 1
                            },
                            {
                                "id": "confidence_3",
                                "label": "How confident are you to make the change?",
                                "type": "slider",
                                "min": 0,
                                "max": 10,
                                "step": 1,
                                "defaultValue": 1
                            }
                        ]
                    }
                ]
            }
        ]
    },
    "source": "physical_activity_attitude",
    "questions": [
        {
            "id": "attitude",
            "title": "Attitude",
            "icon": "attitude",
            "fields": [
                {
                    "id": "exercise_preference",
                    "label": "Do you like doing exercise?",
                    "type": "radio",
                    "options": [
                        "Yes",
                        "No"
                    ],
                    "required": true
                },
                {
                    "id": "reason_for_choice",
                    "label": "Reason for your choice",
                    "type": "section",
                    "fields": [
                        {
                            "id": "knowledge_of_exercise",
                            "label": "Knowledge of exercise",
                            "type": "radio",
                            "options": [
                                "It is good for my health",
                                "I am not sure about the health benefits"
                            ]
                        },
                        {
                            "id": "availability_of_gyms",
                            "label": "Availability of Gyms",
                            "type": "radio",
                            "options": [
                                "It is available in my area",
                                "It is not available in my area"
                            ]
                        },
                        {
                            "id": "affordability_of_gym",
                            "label": "Affordability of Gym",
                            "type": "radio",
                            "options": [
                                "It is within my budget",
                                "It is not within my budget"
                            ]
                        },
                        {
                            "id": "subjective_feeling",
                            "label": "Subjective Feeling",
                            "type": "radio",
                            "options": [
                                "I feel better",
                                "I don't feel better"
                            ]
                        },
                        {
                            "id": "clinical_improvement",
                            "label": "Clinical Improvement",
                            "type": "radio",
                            "options": [
                                "My health parameters are improving",
                                "My health parameters are not improving"
                            ]
                        },
                        {
                            "id": "social_support",
                            "label": "Social Support",
                            "type": "radio",
                            "options": [
                                "I have a strong social support",
                                "I do not have a strong social support"
                            ]
                        }
                    ]
                },
                {
                    "id": "physical_changes",
                    "label": "Rank the top 3 physical changes you want to make",
                    "type": "section",
                    "fields": [
                        {
                            "id": "physical_change_1",
                            "label": "Physical Change 1",
                            "type": "textarea",
                            "placeholder": "Lorem ipsum dolor sit amet, c"
                        },
                        {
                            "id": "importance_1",
                            "label": "How important is it for you to make the change?",
                            "type": "slider",
                            "min": 0,
                            "max": 10,
                            "step": 1,
                            "defaultValue": 1
                        },
                        {
                            "id": "confidence_1",
                            "label": "How confident are you to make the change?",
                            "type": "slider",
                            "min": 0,
                            "max": 10,
                            "step": 1,
                            "defaultValue": 1
                        },
                        {
                            "id": "physical_change_2",
                            "label": "Physical Change 2",
                            "type": "textarea",
                            "placeholder": "Lorem ipsum dolor sit amet, c"
                        },
                        {
                            "id": "importance_2",
                            "label": "How important is it for you to make the change?",
                            "type": "slider",
                            "min": 0,
                            "max": 10,
                            "step": 1,
                            "defaultValue": 1
                        },
                        {
                            "id": "confidence_2",
                            "label": "How confident are you to make the change?",
                            "type": "slider",
                            "min": 0,
                            "max": 10,
                            "step": 1,
                            "defaultValue": 1
                        },
                        {
                            "id": "physical_change_3",
                            "label": "Physical Change 3",
                            "type": "textarea",
                            "placeholder": "Lorem ipsum dolor sit amet, c"
                        },
                        {
                            "id": "importance_3",
                            "label": "How important is it for you to make the change?",
                            "type": "slider",
                            "min": 0,
                            "max": 10,
                            "step": 1,
                            "defaultValue": 1
                        },
                        {
                            "id": "confidence_3",
                            "label": "How confident are you to make the change?",
                            "type": "slider",
                            "min": 0,
                            "max": 10,
                            "step": 1,
                            "defaultValue": 1
                        }
                    ]
                }
            ]
        }
    ],
    "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928",
    "updated_by": "45f8ba73-e782-4152-b23d-26f41c372016",
    "id": "53d0888f-dc54-4214-a529-5a0cd043bf8d",
    "created_on": "2025-07-24T17:19:18.582Z",
    "updated_on": "2025-08-14T08:08:02.105Z",
    "_rid": "gG1xALwxpSQ1AAAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xALwxpSQ=/docs/gG1xALwxpSQ1AAAAAAAAAA==/",
    "_etag": "\"650089fa-0000-0200-0000-689d99620000\"",
    "_attachments": "attachments/",
    "_ts": 1755158882
}


{
    "source": "nutrition_knowledge",
    "questions": [
        {
            "id": "nutrition_knowledge_questionnaire",
            "title": "Nutrition Knowledge Questionnaire",
            "icon": "material-symbols:quiz-outline",
            "fields": [
                {
                    "id": "whole_plant_foods",
                    "label": "Whole plant foods are foods as they exist in nature",
                    "type": "radio",
                    "options": [
                        "Yes",
                        "No",
                        "Not Sure"
                    ]
                },
                {
                    "id": "high_carb_food",
                    "label": "Choose a food with high carbohydrate content",
                    "type": "radio",
                    "options": [
                        "Rice",
                        "Cabbage",
                        "Figs",
                        "Not Sure"
                    ]
                },
                {
                    "id": "ragi_substitute",
                    "label": "Choose a food which is a good substitute for ragi",
                    "type": "radio",
                    "options": [
                        "White Rice",
                        "Red Rice",
                        "Noodles",
                        "Not Sure"
                    ]
                },
                {
                    "id": "processed_vs_natural",
                    "label": "When compared to natural foods, processed foods are",
                    "type": "radio",
                    "options": [
                        "High in Calories",
                        "High in Nutrients",
                        "High in Fibres",
                        "Not Sure"
                    ]
                },
                {
                    "id": "high_protein_food",
                    "label": "Choose a food with high protein content",
                    "type": "radio",
                    "options": [
                        "Oats",
                        "Pasta",
                        "Broccoli",
                        "Not Sure"
                    ]
                },
                {
                    "id": "unsaturated_fat_oil",
                    "label": "Choose an oil with high unsaturated fat content",
                    "type": "radio",
                    "options": [
                        "Sunflower Oil",
                        "Ghee",
                        "Butter/Cheese",
                        "Not Sure"
                    ]
                },
                {
                    "id": "saturated_fat_food",
                    "label": "Choose a food with high saturated fat content",
                    "type": "radio",
                    "options": [
                        "Meat",
                        "Olive Oil",
                        "Nuts",
                        "Not Sure"
                    ]
                },
                {
                    "id": "healthiest_fat",
                    "label": "Choose the most healthy fat",
                    "type": "radio",
                    "options": [
                        "Saturated Fats",
                        "Unsaturated Fats",
                        "Trans/Fatty Acids",
                        "Not Sure"
                    ]
                },
                {
                    "id": "vitamin_mineral_food",
                    "label": "Choose a food with high vitamin & mineral content",
                    "type": "radio",
                    "options": [
                        "Leafy Vegetables",
                        "Whole Grains",
                        "Pulses",
                        "Not Sure"
                    ]
                },
                {
                    "id": "daily_servings",
                    "label": "Recommended daily servings of fruits & vegetables are",
                    "type": "radio",
                    "options": [
                        "3 Servings",
                        "4 Servings",
                        "5 Servings",
                        "Not Sure"
                    ]
                },
                {
                    "id": "high_fiber_food",
                    "label": "Choose a food which is high in fibre content",
                    "type": "radio",
                    "options": [
                        "Meat",
                        "Pulses",
                        "White Rice",
                        "Not Sure"
                    ]
                },
                {
                    "id": "high_salt_food",
                    "label": "Choose a food which is high in salt content",
                    "type": "radio",
                    "options": [
                        "Canned Foods",
                        "Bread",
                        "Vegetables",
                        "Not Sure"
                    ]
                },
                {
                    "id": "best_cooking_method",
                    "label": "Choose the best cooking method",
                    "type": "radio",
                    "options": [
                        "Steaming",
                        "Boiling",
                        "Sauting",
                        "Not sure"
                    ]
                },
                {
                    "id": "nuts_expensive",
                    "label": "Eating nuts are expensive with marginal benefits",
                    "type": "radio",
                    "options": [
                        "Yes",
                        "No",
                        "Not Sure"
                    ]
                },
                {
                    "id": "artificial_sweeteners",
                    "label": "Artificial sweeteners are safe & healthy",
                    "type": "radio",
                    "options": [
                        "Yes",
                        "No",
                        "Not Sure"
                    ]
                },
                {
                    "id": "longevity_meals",
                    "label": "How many meals is associated with longevity",
                    "type": "radio",
                    "options": [
                        "1",
                        "2-3",
                        "4",
                        "Not Sure"
                    ]
                },
                {
                    "id": "important_meal",
                    "label": "The most important meal of the day is",
                    "type": "radio",
                    "options": [
                        "Breakfast",
                        "Lunch",
                        "Dinner",
                        "Not Sure"
                    ]
                },
                {
                    "id": "saturated_fat_heart_disease",
                    "label": "Saturated fats increases the risk for heart disease",
                    "type": "radio",
                    "options": [
                        "1 Litre",
                        "2 Litre",
                        "3 Litre",
                        "Not Sure"
                    ]
                },
                {
                    "id": "water_intake",
                    "label": "Recommended water intake for adults per day is",
                    "type": "radio",
                    "options": [
                        "1 Litre",
                        "2 Litre",
                        "3 Litre",
                        "Not Sure"
                    ]
                },
                {
                    "id": "fiber_prevents_disease",
                    "label": "High fibre intake prevents all the following disease",
                    "type": "radio",
                    "options": [
                        "Heart Disease",
                        "Cancer",
                        "Obesity",
                        "Asthma"
                    ]
                },
                {
                    "id": "salt_disease_risk",
                    "label": "High salt intake increases the risk of following disease",
                    "type": "radio",
                    "options": [
                        "Diabetes",
                        "Hypertension",
                        "Cancer",
                        "Not sure"
                    ]
                }
            ]
        }
    ],
    "created_by": "45f8ba73-e782-4152-b23d-26f41c372016",
    "updated_by": "45f8ba73-e782-4152-b23d-26f41c372016",
    "id": "4fe673d6-fe0a-4fec-aabe-3945e34f1ffd",
    "created_on": "2025-08-04T06:24:10.400Z",
    "updated_on": "2025-08-05T09:51:48.821Z",
    "_rid": "gG1xALwxpSQ5AAAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xALwxpSQ=/docs/gG1xALwxpSQ5AAAAAAAAAA==/",
    "_etag": "\"9f026b59-0000-0200-0000-6891d4340000\"",
    "_attachments": "attachments/",
    "_ts": 1754387508
}

{
    "source": "physical_activity_practice_exercise_patterns",
    "questions": [
        {
            "id": "exercise_patterns",
            "title": "Exercise Patterns",
            "icon": "dumbbell",
            "fields": [
                {
                    "id": "exercise_table",
                    "label": "Exercise Activities",
                    "type": "table",
                    "headers": [
                        {
                            "id": "activity_type",
                            "label": "Activity Type",
                            "type": "select",
                            "options": [
                                "Aerobics",
                                "Strength",
                                "Flexibility",
                                "Balance"
                            ]
                        },
                        {
                            "id": "activity",
                            "label": "Activity",
                            "type": "conditional_select",
                            "dependsOn": "activity_type",
                            "options": {
                                "Aerobics": [
                                    "Walking",
                                    "Jogging",
                                    "Running",
                                    "Cycling",
                                    "Swimming",
                                    "Dancing",
                                    "Zumba",
                                    "Skipping",
                                    "Kickboxing",
                                    "Rowing",
                                    "Hiking",
                                    "Stair Climbing",
                                    "Sports-Cricket,Basketball,Football,Badminton,Lawn Tennis,Golf"
                                ],
                                "Strength": [
                                    "Body Weight Exercises",
                                    "Free Weight Exercises",
                                    "Machine Based Exercise",
                                    "Resistance Band Exercise",
                                    "Functional Strenght Exercise"
                                ],
                                "Flexibility": [
                                    "Static Stretching",
                                    "Dynamic Stretching",
                                    "Yoga",
                                    "Pilates based flexibility",
                                    "Foam Rolling"
                                ],
                                "Balance": [
                                    "Basic Balance Exercise",
                                    "Balance Enhancing Yoga Poses",
                                    "Advanced Functional Balance Exercise",
                                    "Sports Specific or dynamic Balance drills",
                                    "Foam Rolling"
                                ]
                            }
                        },
                        {
                            "id": "duration",
                            "label": "Duration(min)",
                            "type": "number"
                        },
                        {
                            "id": "intensity",
                            "label": "Intensity",
                            "type": "select",
                            "options": [
                                "Mild",
                                "Moderate",
                                "Intense"
                            ]
                        },
                        {
                            "id": "frequency",
                            "label": "Frequency",
                            "type": "select",
                            "options": [
                                "Daily",
                                "Three times a week",
                                "Four times a week",
                                "Five times a week",
                                "Six times a week"
                            ]
                        }
                    ]
                }
            ]
        }
    ],
    "created_by": "local_debugging",
    "updated_by": "45f8ba73-e782-4152-b23d-26f41c372016",
    "id": "0bfd4279-8dc7-4f6f-b3ec-ae3ce9bea44d",
    "created_on": "2025-07-24T07:50:59.967Z",
    "updated_on": "2025-08-01T11:15:19.813Z",
    "_rid": "gG1xALwxpSQ0AAAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xALwxpSQ=/docs/gG1xALwxpSQ0AAAAAAAAAA==/",
    "_etag": "\"5f011047-0000-0200-0000-688ca1c70000\"",
    "_attachments": "attachments/",
    "_ts": **********
}

{
    "source": "nutrition_attitude",
    "questions": [
        {
            "id": "attitude",
            "title": "Attitude",
            "icon": "material-symbols:psychology-outline",
            "fields": [
                {
                    "id": "eating_healthy_food",
                    "label": "Do you like eating healthy food?",
                    "type": "radio",
                    "options": [
                        "Yes",
                        "No"
                    ]
                },
                {
                    "id": "reason_for_choice",
                    "label": "Reason for your choice",
                    "type": "section",
                    "fields": [
                        {
                            "id": "knowledge_of_healthy_food",
                            "label": "Knowledge of healthy food",
                            "type": "radio",
                            "options": [
                                "It is good for my health",
                                "I am not sure about the health benefits"
                            ]
                        },
                        {
                            "id": "availability_of_healthy_food",
                            "label": "Availability of healthy food",
                            "type": "radio",
                            "options": [
                                "It is available in my area",
                                "It is not available in my area"
                            ]
                        },
                        {
                            "id": "affordability_of_healthy_food",
                            "label": "Affordability of healthy food",
                            "type": "radio",
                            "options": [
                                "It is within my budget",
                                "It is not within my budget"
                            ]
                        },
                        {
                            "id": "subjective_feeling",
                            "label": "Subjective Feeling",
                            "type": "radio",
                            "options": [
                                "I feel better",
                                "I don't feel better"
                            ]
                        },
                        {
                            "id": "clinical_improvement",
                            "label": "Clinical Improvement",
                            "type": "radio",
                            "options": [
                                "My health parameters are improving",
                                "My health parameters are not improving"
                            ]
                        },
                        {
                            "id": "social_support",
                            "label": "Social Support",
                            "type": "radio",
                            "options": [
                                "I have a strong social support",
                                "I do not have a strong social support"
                            ]
                        }
                    ]
                },
                {
                    "id": "nutritional_changes_ranking",
                    "label": "Rank the top 3 nutritional changes you want to make",
                    "type": "section",
                    "fields": [
                        {
                            "id": "nutritional_change_1",
                            "label": "Nutritional Change 1",
                            "type": "textarea",
                            "placeholder": ""
                        },
                        {
                            "id": "importance_change_1",
                            "label": "How important is it for you to make the change?",
                            "type": "slider",
                            "min": 0,
                            "max": 10,
                            "step": 1,
                            "description": "(0=not important to 10=very important)"
                        },
                        {
                            "id": "confidence_change_1",
                            "label": "How confident are you to make the change?",
                            "type": "slider",
                            "min": 0,
                            "max": 10,
                            "step": 1,
                            "description": "(0=not confident to 10=very confident)"
                        },
                        {
                            "id": "nutritional_change_2",
                            "label": "Nutritional Change 2",
                            "type": "textarea",
                            "placeholder": ""
                        },
                        {
                            "id": "importance_change_2",
                            "label": "How important is it for you to make the change?",
                            "type": "slider",
                            "min": 0,
                            "max": 10,
                            "step": 1,
                            "description": "(0=not important to 10=very important)"
                        },
                        {
                            "id": "confidence_change_2",
                            "label": "How confident are you to make the change?",
                            "type": "slider",
                            "min": 0,
                            "max": 10,
                            "step": 1,
                            "description": "(0=not confident to 10=very confident)"
                        },
                        {
                            "id": "nutritional_change_3",
                            "label": "Nutritional Change 3",
                            "type": "textarea",
                            "placeholder": ""
                        },
                        {
                            "id": "importance_change_3",
                            "label": "How important is it for you to make the change?",
                            "type": "slider",
                            "min": 0,
                            "max": 10,
                            "step": 1,
                            "description": "(0=not important to 10=very important)"
                        },
                        {
                            "id": "confidence_change_3",
                            "label": "How confident are you to make the change?",
                            "type": "slider",
                            "min": 0,
                            "max": 10,
                            "step": 1,
                            "description": "(0=not confident to 10=very confident)"
                        }
                    ]
                }
            ]
        }
    ],
    "created_by": "45f8ba73-e782-4152-b23d-26f41c372016",
    "updated_by": "45f8ba73-e782-4152-b23d-26f41c372016",
    "id": "fdde1819-3ec7-41c8-8f17-2361c01b9755",
    "created_on": "2025-07-29T07:00:28.024Z",
    "updated_on": "2025-07-29T07:00:28.024Z",
    "_rid": "gG1xALwxpSQ4AAAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xALwxpSQ=/docs/gG1xALwxpSQ4AAAAAAAAAA==/",
    "_etag": "\"f1008fd2-0000-0200-0000-6888718c0000\"",
    "_attachments": "attachments/",
    "_ts": 1753772428
}

{
    "source": "nutrition_practice_food_frequency_questionnaire",
    "questions": [
        {
            "id": "food_frequency_questionnaire",
            "title": "Food Frequency Questionnaire",
            "icon": "material-symbols:checklist-rtl-rounded",
            "sections": [
                {
                    "id": "cereals_grains",
                    "title": "Cereals & Grains",
                    "icon": "material-symbols:checklist-rtl-rounded",
                    "fields": [
                        {
                            "id": "white_bread",
                            "label": "White Bread",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "whole_wheat_bread",
                            "label": "Whole Wheat Bread",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "oats",
                            "label": "Oats",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "rice",
                            "label": "Rice",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "rice_bran",
                            "label": "Rice Bran",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "maida",
                            "label": "Maida",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "dal",
                            "label": "Dal",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "rava",
                            "label": "Rava",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "rice_flakes",
                            "label": "Rice Flakes",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "vermicelli",
                            "label": "Vermicelli",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "moong_products",
                            "label": "Moong & products",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "wheat",
                            "label": "Wheat",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "pasta",
                            "label": "Pasta",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "wheat_dalia",
                            "label": "Wheat dalia",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "brown_rice",
                            "label": "Brown Rice",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "red_rice",
                            "label": "Red Rice",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "puffed_rice",
                            "label": "Puffed Rice",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "chapati",
                            "label": "Chapati",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "other_millets",
                            "label": "Other Millets (Foxtail millets,Little millet,Barnyard millet)",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    "id": "pulses_legumes",
                    "title": "Pulses & Legumes",
                    "icon": "material-symbols:grain",
                    "fields": [
                        {
                            "id": "red_chori_dal",
                            "label": "Red Chori Dal",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "besan_gram_dal",
                            "label": "Besan/Gram Dal",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "green_gram_dal",
                            "label": "Green Gram Dal",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "urad_dal",
                            "label": "Urad Dal",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "masoor_dal",
                            "label": "Masoor Dal",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "cow_peas",
                            "label": "Cow Peas",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "chana_roast",
                            "label": "Chana Roast",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "horse_bean",
                            "label": "Horse Bean",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    "id": "non_veg",
                    "title": "Non Veg",
                    "icon": "material-symbols:egg",
                    "fields": [
                        {
                            "id": "poultry",
                            "label": "Poultry",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "mutton",
                            "label": "Mutton",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "fish",
                            "label": "Fish",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "egg",
                            "label": "Egg",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    "id": "legumes_beans",
                    "title": "Legumes & Beans",
                    "icon": "material-symbols:grain",
                    "fields": [
                        {
                            "id": "white_channa",
                            "label": "White Channa",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "chick_peas",
                            "label": "Chick Peas",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "soya_bean",
                            "label": "Soya Bean",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    "id": "vegetables_fruits",
                    "title": "Vegetables & Fruits",
                    "icon": "material-symbols:nutrition",
                    "fields": [
                        {
                            "id": "green_leafy_vegetables",
                            "label": "Green Leafy Vegetables",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "roots_tubers",
                            "label": "Roots & Tubers",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "other_vegetables",
                            "label": "Other Vegetables",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "fruits",
                            "label": "Fruits",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    "id": "dairy_products",
                    "title": "Dairy Products",
                    "icon": "material-symbols:water-drop",
                    "fields": [
                        {
                            "id": "milk",
                            "label": "Milk",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "curd",
                            "label": "Curd",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "paneer",
                            "label": "Paneer",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "cheese",
                            "label": "Cheese",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    "id": "fats_oils",
                    "title": "Fats & Oils",
                    "icon": "material-symbols:water-drop",
                    "fields": [
                        {
                            "id": "vegetable_oil",
                            "label": "Vegetable Oil (used for Cooking)",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "ghee",
                            "label": "Ghee",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "vanaspati",
                            "label": "Vanaspati",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "coconut_oil",
                            "label": "Coconut Oil",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "mustard_oil",
                            "label": "Mustard Oil",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    "id": "nuts",
                    "title": "Nuts",
                    "icon": "material-symbols:nutrition",
                    "fields": [
                        {
                            "id": "groundnuts",
                            "label": "Groundnuts",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "cashew_nuts",
                            "label": "Cashew Nuts",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "walnuts",
                            "label": "Walnuts",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    "id": "seeds",
                    "title": "Seeds",
                    "icon": "material-symbols:grain",
                    "fields": [
                        {
                            "id": "chia_seeds",
                            "label": "Chia Seeds",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "flax_seeds",
                            "label": "Flax Seeds",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "pumpkin_seeds",
                            "label": "Pumpkin Seeds",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "sunflower_seeds",
                            "label": "Sunflower Seeds",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    "id": "sugars",
                    "title": "Sugars",
                    "icon": "material-symbols:cake",
                    "fields": [
                        {
                            "id": "white_sugar",
                            "label": "White Sugar",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "jaggery",
                            "label": "Jaggery",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "honey",
                            "label": "Honey",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    "id": "beverages",
                    "title": "Beverages",
                    "icon": "material-symbols:coffee-outline-rounded",
                    "fields": [
                        {
                            "id": "water",
                            "label": "Water",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "tea",
                            "label": "Tea",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "coffee",
                            "label": "Coffee",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "herbal_tea",
                            "label": "Herbal Tea",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "green_tea",
                            "label": "Green Tea",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "black_tea",
                            "label": "Black Tea",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "soft_drinks",
                            "label": "Soft Drinks (Pepsi, Coke, Sprite etc)",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    "id": "others",
                    "title": "Others",
                    "icon": "material-symbols:restaurant",
                    "fields": [
                        {
                            "id": "pizza",
                            "label": "Pizza",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "fried_foods",
                            "label": "Fried Foods (Samosa, Vada, Chips, etc.)",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "pickle",
                            "label": "Pickle",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        },
                        {
                            "id": "sweets_products",
                            "label": "Sweets Products (Cake, Biscuits, Ras, Peda, Barfi)",
                            "type": "frequency",
                            "columns": [
                                {
                                    "header": "Daily",
                                    "option": [
                                        "daily"
                                    ]
                                },
                                {
                                    "header": "Weekly (1,2,3,4,5)",
                                    "option": [
                                        "weekly_1",
                                        "weekly_2",
                                        "weekly_3",
                                        "weekly_4",
                                        "weekly_5"
                                    ]
                                },
                                {
                                    "header": "Monthly (1,2)",
                                    "option": [
                                        "monthly_1",
                                        "monthly_2"
                                    ]
                                },
                                {
                                    "header": "Rarely",
                                    "option": [
                                        "rarely"
                                    ]
                                },
                                {
                                    "header": "Never",
                                    "option": [
                                        "never"
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    ],
    "created_by": "local_debugging",
    "updated_by": "local_debugging",
    "id": "fb20154b-b013-4a38-91a9-d1ebea1a0d79",
    "created_on": "2025-07-28T06:34:23.839Z",
    "updated_on": "2025-07-28T09:36:49.623Z",
    "_rid": "gG1xALwxpSQ3AAAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xALwxpSQ=/docs/gG1xALwxpSQ3AAAAAAAAAA==/",
    "_etag": "\"a700e059-0000-0200-0000-688744b30000\"",
    "_attachments": "attachments/",
    "_ts": 1753695411
}

{
    "source": "physical_activity_knowledge",
    "questions": [
        {
            "id": "physical_activity_knowledge_questionnaire",
            "title": "Physical Activity Knowledge Questionnaire",
            "icon": "material-symbols:quiz-outline",
            "fields": [
                {
                    "id": "exercise_benefits",
                    "label": "The benefit of exercise is all except",
                    "type": "radio",
                    "options": [
                        "Increase blood sugar",
                        "Weight Reduction",
                        "Improve Immunity",
                        "Reduce Stress"
                    ]
                },
                {
                    "id": "aerobic_exercise_examples",
                    "label": "Examples of Aerobic Exercise",
                    "type": "radio",
                    "options": [
                        "Weight Lifting",
                        "Push up",
                        "Swimming",
                        "Yoga"
                    ]
                },
                {
                    "id": "strength_training_examples",
                    "label": "Examples of Strength Training exercise",
                    "type": "radio",
                    "options": [
                        "Cycling",
                        "Sprinting",
                        "Hill Climbing",
                        "Planks"
                    ]
                },
                {
                    "id": "balance_training_beneficial",
                    "label": "Balance Training is most beneficial for",
                    "type": "radio",
                    "options": [
                        "Adolescents",
                        "Office Workers",
                        "Older Adults",
                        "Endurance Athletes"
                    ]
                },
                {
                    "id": "flexibility_exercise_type",
                    "label": "Which type of exercise is primarily aimed at improving flexibility",
                    "type": "radio",
                    "options": [
                        "Swimming",
                        "Sprinting",
                        "Rowing",
                        "Yoga"
                    ]
                },
                {
                    "id": "moderate_aerobic_recommendation",
                    "label": "Minimum recommendation for moderate intensity aerobic activity in adults",
                    "type": "radio",
                    "options": [
                        "30 minutes per week",
                        "60 minutes per week",
                        "90 minutes per week",
                        "150 minutes per week"
                    ]
                },
                {
                    "id": "strength_training_recommendation",
                    "label": "Minimum recommendation for strength training in adults",
                    "type": "radio",
                    "options": [
                        "Once per week",
                        "Twice per week",
                        "Daily",
                        "Twice Daily"
                    ]
                },
                {
                    "id": "moderate_intensity_activity",
                    "label": "Which of the following is considered a moderate intensity activity",
                    "type": "radio",
                    "options": [
                        "Leisurely Walking",
                        "Gardening or Brisk Walking",
                        "Sprinting",
                        "Power Lifting"
                    ]
                },
                {
                    "id": "warm_up_reason",
                    "label": "What is the primary reason for warming up",
                    "type": "radio",
                    "options": [
                        "Increase Blood Pressure",
                        "Improve Sweat Production",
                        "Prepare Muscles & Reduce Injury Risks",
                        "Burn more Calories"
                    ]
                },
                {
                    "id": "workout_clothing_material",
                    "label": "Which material is best for workout clothing",
                    "type": "radio",
                    "options": [
                        "Wool",
                        "Cotton",
                        "Denim",
                        "Polyester Blends"
                    ]
                },
                {
                    "id": "walking_after_food_helpful",
                    "label": "Walking after food is helpful in patients with",
                    "type": "radio",
                    "options": [
                        "Heart Disease",
                        "Diabetes Mellitus",
                        "Elderly",
                        "Peptic Ulcer"
                    ]
                },
                {
                    "id": "bone_health_exercise",
                    "label": "Exercise that helps in bone health",
                    "type": "radio",
                    "options": [
                        "Walking",
                        "Swimming",
                        "Cycling",
                        "Yoga"
                    ]
                },
                {
                    "id": "muscle_mass_exercise",
                    "label": "Which exercises increases the muscle mass",
                    "type": "radio",
                    "options": [
                        "Aerobic Exercise",
                        "Flexibility Exercise",
                        "Strength Training",
                        "Balance Exercise"
                    ]
                },
                {
                    "id": "heart_function_exercise",
                    "label": "Exercise that improves heart function",
                    "type": "radio",
                    "options": [
                        "Aerobic Exercise",
                        "Flexibility Exercise",
                        "Strength Training",
                        "Balance Exercise"
                    ]
                },
                {
                    "id": "ideal_pre_exercise_food",
                    "label": "The ideal food to be eaten before exercise",
                    "type": "radio",
                    "options": [
                        "Fatty Food",
                        "Sugary Beverage",
                        "Banana",
                        "Raw Vegetables"
                    ]
                }
            ]
        }
    ],
    "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928",
    "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928",
    "id": "f4b519f6-ebad-4545-b09f-c5ce94d82d92",
    "created_on": "2025-07-25T11:22:58.380Z",
    "updated_on": "2025-07-25T11:22:58.380Z",
    "_rid": "gG1xALwxpSQ2AAAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xALwxpSQ=/docs/gG1xALwxpSQ2AAAAAAAAAA==/",
    "_etag": "\"11001fee-0000-0200-0000-688369120000\"",
    "_attachments": "attachments/",
    "_ts": 1753442578
}

{
    "source": "nutrition_practice_dietary_recall",
    "questions": [
        {
            "id": "dietary_recall",
            "title": "24 hour Dietary Recall",
            "icon": "mdi:clock-outline",
            "fields": [
                {
                    "id": "dietary_table",
                    "label": "Food Intake",
                    "type": "grouped_table",
                    "headers": [
                        {
                            "id": "time_range",
                            "label": "Timings",
                            "type": "time_range"
                        },
                        {
                            "id": "food_item",
                            "label": "Food Consumed",
                            "type": "text"
                        },
                        {
                            "id": "serving_type",
                            "label": "Servings type",
                            "type": "select",
                            "options": [
                                "Number",
                                "Bowl",
                                "Spoons",
                                "Cups"
                            ]
                        },
                        {
                            "id": "quantity",
                            "label": "Quantity",
                            "type": "number",
                            "min": 1
                        },
                        {
                            "id": "icon",
                            "label": "",
                            "type": "icon"
                        }
                    ],
                    "groupBy": "meal",
                    "mealGroups": [
                        {
                            "id": "breakfast",
                            "label": "Breakfast",
                            "defaultRows": []
                        },
                        {
                            "id": "mid_morning",
                            "label": "Mid-morning",
                            "defaultRows": []
                        },
                        {
                            "id": "lunch",
                            "label": "Lunch",
                            "defaultRows": []
                        },
                        {
                            "id": "evening_snacks",
                            "label": "Evening snacks",
                            "defaultRows": []
                        },
                        {
                            "id": "dinner",
                            "label": "Dinner",
                            "defaultRows": []
                        },
                        {
                            "id": "post_dinner_snacks",
                            "label": "Post-dinner snacks",
                            "defaultRows": []
                        }
                    ]
                }
            ]
        }
    ],
    "created_by": "local_debugging",
    "updated_by": "local_debugging",
    "id": "f778a42b-8f5b-4f57-b95b-f227cb230cab",
    "created_on": "2025-07-24T07:40:43.060Z",
    "updated_on": "2025-07-25T11:09:17.319Z",
    "_rid": "gG1xALwxpSQzAAAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xALwxpSQ=/docs/gG1xALwxpSQzAAAAAAAAAA==/",
    "_etag": "\"1100fcc4-0000-0200-0000-688365dd0000\"",
    "_attachments": "attachments/",
    "_ts": 1753441757
}

{
    "source": "nutrition_practice_food_intake_patterns",
    "questions": [
        {
            "id": "food_intake",
            "title": "Food intake patterns",
            "icon": "material-symbols:dining-outline",
            "fields": [
                {
                    "id": "major_meals_per_day",
                    "label": "Number of major meals per day",
                    "type": "radio",
                    "options": [
                        "1",
                        "2",
                        "3",
                        "4",
                        "Other"
                    ],
                    "allowOtherSpecify": true,
                    "modal": {
                        "title": "Meals per day",
                        "inputType": "number"
                    }
                },
                {
                    "id": "snacks_per_day",
                    "label": "Number of snacks per day",
                    "type": "radio",
                    "options": [
                        "1",
                        "2",
                        "3",
                        "4",
                        "5"
                    ]
                },
                {
                    "id": "missed_meal",
                    "label": "Which meal do you skip/miss most often in a day?",
                    "type": "radio",
                    "options": [
                        "Breakfast",
                        "Lunch",
                        "Dinner",
                        "Others"
                    ],
                    "allowOtherSpecify": true,
                    "modal": {
                        "title": "Meal Type",
                        "inputType": "text"
                    }
                },
                {
                    "id": "family_meals",
                    "label": "How many meals do you eat with family in a day?",
                    "type": "radio",
                    "options": [
                        "0",
                        "1",
                        "2",
                        "3",
                        "4"
                    ]
                },
                {
                    "id": "tv_meals",
                    "label": "How many meals do you eat while watching TV in a day?",
                    "type": "radio",
                    "options": [
                        "0",
                        "1",
                        "2",
                        "3",
                        "4"
                    ]
                },
                {
                    "id": "taste_rating",
                    "label": "How do you rate the taste of your meals?",
                    "type": "slider",
                    "min": 0,
                    "max": 10,
                    "step": 1,
                    "description": "(0=Unpleasant to 10=Delicious)"
                },
                {
                    "id": "satiety_rating",
                    "label": "How do you rate the satiety of your meals?",
                    "type": "slider",
                    "min": 0,
                    "max": 10,
                    "step": 1,
                    "description": "(0=Not filling to 10=Overly full)"
                }
            ]
        },
        {
            "id": "beverage_intake",
            "title": "Beverage intake patterns",
            "icon": "material-symbols:coffee-outline-rounded",
            "fields": [
                {
                    "id": "tea",
                    "label": "Tea",
                    "type": "conditional",
                    "conditions": [
                        {
                            "label": "Yes",
                            "subField": {
                                "id": "tea_cups_per_day",
                                "label": "Cups per Day",
                                "type": "number",
                                "min": 1,
                                "max": 10
                            }
                        },
                        {
                            "label": "No"
                        }
                    ]
                },
                {
                    "id": "coffee",
                    "label": "Coffee",
                    "type": "conditional",
                    "conditions": [
                        {
                            "label": "Yes",
                            "subField": {
                                "id": "coffee_cups_per_day",
                                "label": "Cups per Day",
                                "type": "number",
                                "min": 1,
                                "max": 10
                            }
                        },
                        {
                            "label": "No"
                        }
                    ]
                }
            ]
        }
    ],
    "created_by": "local_debugging",
    "updated_by": "local_debugging",
    "id": "50c59f01-e436-4055-b0e7-cf975ad9d48d",
    "created_on": "2025-07-18T06:01:35.468Z",
    "updated_on": "2025-07-23T13:01:19.900Z",
    "_rid": "gG1xALwxpSQxAAAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xALwxpSQ=/docs/gG1xALwxpSQxAAAAAAAAAA==/",
    "_etag": "\"bc0573cd-0000-0200-0000-6880dd220000\"",
    "_attachments": "attachments/",
    "_ts": 1753275682
}

{
    "source": "nutrition_monitoring_sheet",
    "sections": [],
    "created_by": "262c518a-478f-41b8-ab77-01625fe8aca8",
    "updated_by": "262c518a-478f-41b8-ab77-01625fe8aca8",
    "id": "7f5e79d7-4f80-4fcb-8291-6f8dee5cdb32",
    "created_on": "2025-03-17T08:43:00.644Z",
    "updated_on": "2025-03-19T18:58:05.370Z",
    "_rid": "gG1xALwxpSQYAAAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xALwxpSQ=/docs/gG1xALwxpSQYAAAAAAAAAA==/",
    "_etag": "\"4a01c584-0000-0200-0000-67db13bd0000\"",
    "_attachments": "attachments/",
    "section_id": "section_regular_home_food_intake_pattern",
    "section_title": "Regular home food intake pattern",
    "questions": [
        {
            "order": 1,
            "question": "",
            "type": "table_header",
            "sub_questions": [
                {
                    "order": 1,
                    "type": "table_header",
                    "question": "Meal Patter",
                    "headerKey": "mealPattern"
                },
                {
                    "order": 2,
                    "type": "table_header",
                    "question": "Timings",
                    "headerKey": "timings"
                },
                {
                    "order": 3,
                    "type": "table_header",
                    "question": "Food Consumed",
                    "headerKey": "foodConsumed"
                },
                {
                    "order": 4,
                    "type": "table_header",
                    "question": "Quantity",
                    "headerKey": "quantity"
                }
            ]
        },
        {
            "order": 2,
            "question": "",
            "type": "table_row",
            "sub_questions": [
                {
                    "order": 1,
                    "question": "Break Fast",
                    "headerKey": "mealPattern",
                    "type": "table_header"
                },
                {
                    "order": 2,
                    "question": "",
                    "headerKey": "timings",
                    "type": "multi_time_range",
                    "defaultValue": {
                        "from": "",
                        "to": ""
                    },
                    "sub_questions": [
                        {
                            "order": 1,
                            "question": "timings",
                            "type": "time_range",
                            "value": {
                                "from": "",
                                "to": ""
                            }
                        }
                    ]
                },
                {
                    "order": 3,
                    "question": "",
                    "headerKey": "foodConsumed",
                    "type": "multi_text",
                    "defaultValue": " ",
                    "sub_questions": [
                        {
                            "order": 1,
                            "question": "foodConsumed",
                            "type": "text",
                            "value": ""
                        }
                    ]
                },
                {
                    "order": 4,
                    "question": "",
                    "headerKey": "quantity",
                    "type": "multi_number",
                    "defaultValue": "",
                    "sub_questions": [
                        {
                            "order": 1,
                            "question": "quantity",
                            "type": "number",
                            "value": ""
                        }
                    ]
                }
            ]
        },
        {
            "order": 3,
            "question": "",
            "type": "table_row",
            "sub_questions": [
                {
                    "order": 1,
                    "question": "Mid-Morning",
                    "headerKey": "mealPattern",
                    "type": "table_header"
                },
                {
                    "order": 2,
                    "question": "",
                    "headerKey": "timings",
                    "type": "multi_time_range",
                    "defaultValue": {
                        "from": "",
                        "to": ""
                    },
                    "sub_questions": [
                        {
                            "order": 1,
                            "question": "timings",
                            "type": "time_range",
                            "value": {
                                "from": "",
                                "to": ""
                            }
                        }
                    ]
                },
                {
                    "order": 3,
                    "question": "",
                    "headerKey": "foodConsumed",
                    "type": "multi_text",
                    "defaultValue": " ",
                    "sub_questions": [
                        {
                            "order": 1,
                            "question": "foodConsumed",
                            "type": "text",
                            "value": ""
                        }
                    ]
                },
                {
                    "order": 4,
                    "question": "",
                    "headerKey": "quantity",
                    "type": "multi_number",
                    "defaultValue": "",
                    "sub_questions": [
                        {
                            "order": 1,
                            "question": "quantity",
                            "type": "number",
                            "value": ""
                        }
                    ]
                }
            ]
        },
        {
            "order": 4,
            "question": "",
            "type": "table_row",
            "sub_questions": [
                {
                    "order": 1,
                    "question": "Lunch",
                    "headerKey": "mealPattern",
                    "type": "table_header"
                },
                {
                    "order": 2,
                    "question": "",
                    "headerKey": "timings",
                    "type": "multi_time_range",
                    "defaultValue": {
                        "from": "",
                        "to": ""
                    },
                    "sub_questions": [
                        {
                            "order": 1,
                            "question": "timings",
                            "type": "time_range",
                            "value": {
                                "from": "",
                                "to": ""
                            }
                        }
                    ]
                },
                {
                    "order": 3,
                    "question": "",
                    "headerKey": "foodConsumed",
                    "type": "multi_text",
                    "defaultValue": " ",
                    "sub_questions": [
                        {
                            "order": 1,
                            "question": "foodConsumed",
                            "type": "text",
                            "value": ""
                        }
                    ]
                },
                {
                    "order": 4,
                    "question": "",
                    "headerKey": "quantity",
                    "type": "multi_number",
                    "defaultValue": "",
                    "sub_questions": [
                        {
                            "order": 1,
                            "question": "quantity",
                            "type": "number",
                            "value": ""
                        }
                    ]
                }
            ]
        },
        {
            "order": 5,
            "question": "",
            "type": "table_row",
            "sub_questions": [
                {
                    "order": 1,
                    "question": "Evening",
                    "headerKey": "mealPattern",
                    "type": "table_header"
                },
                {
                    "order": 2,
                    "question": "",
                    "headerKey": "timings",
                    "type": "multi_time_range",
                    "defaultValue": {
                        "from": "",
                        "to": ""
                    },
                    "sub_questions": [
                        {
                            "order": 1,
                            "question": "timings",
                            "type": "time_range",
                            "value": {
                                "from": "",
                                "to": ""
                            }
                        }
                    ]
                },
                {
                    "order": 3,
                    "question": "",
                    "headerKey": "foodConsumed",
                    "type": "multi_text",
                    "defaultValue": " ",
                    "sub_questions": [
                        {
                            "order": 1,
                            "question": "foodConsumed",
                            "type": "text",
                            "value": ""
                        }
                    ]
                },
                {
                    "order": 4,
                    "question": "",
                    "headerKey": "quantity",
                    "type": "multi_number",
                    "defaultValue": "",
                    "sub_questions": [
                        {
                            "order": 1,
                            "question": "quantity",
                            "type": "number",
                            "value": ""
                        }
                    ]
                }
            ]
        },
        {
            "order": 6,
            "question": "",
            "type": "table_row",
            "sub_questions": [
                {
                    "order": 1,
                    "question": "Dinner",
                    "headerKey": "mealPattern",
                    "type": "table_header"
                },
                {
                    "order": 2,
                    "question": "",
                    "headerKey": "timings",
                    "type": "multi_time_range",
                    "defaultValue": {
                        "from": "",
                        "to": ""
                    },
                    "sub_questions": [
                        {
                            "order": 1,
                            "question": "timings",
                            "type": "time_range",
                            "value": {
                                "from": "",
                                "to": ""
                            }
                        }
                    ]
                },
                {
                    "order": 3,
                    "question": "",
                    "headerKey": "foodConsumed",
                    "type": "multi_text",
                    "defaultValue": " ",
                    "sub_questions": [
                        {
                            "order": 1,
                            "question": "foodConsumed",
                            "type": "text",
                            "value": ""
                        }
                    ]
                },
                {
                    "order": 4,
                    "question": "",
                    "headerKey": "quantity",
                    "type": "multi_number",
                    "defaultValue": "",
                    "sub_questions": [
                        {
                            "order": 1,
                            "question": "quantity",
                            "type": "number",
                            "value": ""
                        }
                    ]
                }
            ]
        }
    ],
    "type": "table",
    "_ts": **********
}

MedicalHistoryAddiction
---------------------


{
    "_rid": "gG1xAPl6ZhkCAAAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xAPl6Zhk=/docs/gG1xAPl6ZhkCAAAAAAAAAA==/",
    "_etag": "\"f6002d5b-0000-0200-0000-6889cbfc0000\"",
    "_attachments": "attachments/",
    "created_by": "local_debugging",
    "updated_by": "local_debugging",
    "created_on": "2025-07-30T07:33:28.335Z",
    "updated_on": "2025-a07-30T07:38:36.435Z",
    "patientId": "PV29909817",
    "diagnosis": [
        {
            "diseaseName": "45",
            "yearOfDiagnosis": {
                "value": "2025",
                "label": "2025"
            },
            "diagnosisDuration": "df",
            "status": "active",
            "treatmentHistory": "45"
        },
        {
            "diseaseName": "456",
            "yearOfDiagnosis": {
                "value": "2024",
                "label": "2024"
            },
            "diagnosisDuration": "d",
            "status": "inactive",
            "treatmentHistory": "df"
        }
    ],
    "smoking": {
        "history": "former",
        "count": "23",
        "frequency": {
            "value": "2-3_times_week",
            "label": "2–3 Times Week"
        }
    },
    "alcohol": {
        "history": "former",
        "count": "23",
        "frequency": {
            "value": "daily",
            "label": "Daily"
        }
    },
    "tobacco": {
        "history": "current",
        "count": "23",
        "frequency": {
            "value": "daily",
            "label": "Daily"
        }
    },
    "drugs": {
        "history": "current",
        "count": "34",
        "frequency": {
            "value": "2-3_times_week",
            "label": "2–3 Times Week"
        }
    },
    "nicotineDependenceTest": {
        "responses": {
            "timeToFirstCigarette": "More than 60 minutes",
            "findDifficult": "No",
            "whichCigarette": "The first in the morning",
            "cigarettesPerDay": "21-30",
            "moreFrequentMorning": "No",
            "smokeWhenIll": "No"
        },
        "testDate": "2025-07-30T07:35:18.514Z"
    },
    "id": "9c387dc6-d843-4c04-873e-83b996f6fffc",
    "_ts": 1753861116
}


medicines
--------------



{
    "_rid": "gG1xAMEJD7WCHwAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xAMEJD7U=/docs/gG1xAMEJD7WCHwAAAAAAAA==/",
    "_etag": "\"e5014de6-0000-0200-0000-685746f70000\"",
    "_attachments": "attachments/",
    "created_by": "",
    "updated_by": "",
    "created_on": "2025-06-21T23:57:43.818Z",
    "updated_on": "2025-06-21T23:57:43.818Z",
    "productId": "DRS000001",
    "productName": "1 AL AX 5mg/75mg Capsule",
    "marketerOrManufacturer": "FDC Ltd",
    "saltComposition": "Levocetirizine (5mg) + Ambroxol (75mg)",
    "medicineType": "drugs",
    "introduction": "In Cough 1 AL AX 5mg/75mg Capsule gives relief from cough by loosening thick mucus and making it easier to cough it out. It also makes it easier for air to move in and out of your airways. This reduces the frequency of coughing and makes you feel better. 1 AL AX 5mg/75mg Capsule also relieves allergy symptoms like watery eyes, sneezing, runny nose or throat irritation. Along with medications, drink enough lukewarm water and gargle with warm salt water to ease the symptoms.",
    "benefits": "Take this medicine in the dose and duration as advised by your doctor. 1 AL AX 5mg/75mg Capsule may be taken with or without food, but it is better to take it at a fixed time.",
    "description": "1 AL AX 5mg/75mg Capsule is a combination medicine used in the treatment of cough. It relieves allergic symptoms such as sneezing, running nose, watery eyes, itching, swelling, and congestion or stiffness. It also thins and loosens mucus, making it easier to cough out. 1 AL AX 5mg/75mg Capsule is taken with or without food in a dose and duration as advised by the doctor. The dose you are given will depend on your condition and how you respond to the medicine. You should keep taking this medicine for as long as your doctor recommends. If you stop treatment too early your symptoms may come back and your condition may worsen. Let your doctor know about all other medications you are taking as some may affect, or be affected by this medicine.The most common side effects are stomach upset, dryness in mouth, headache, fatigue, and allergic reaction. Most of these are temporary and usually resolve with time. Contact your doctor straight away if you are at all concerned about any of these side effects. This medicine can also cause dizziness and sleepiness, so do not drive or do anything that requires mental focus until you know how this medicine affects you. Avoid drinking alcohol while taking this medicine as it can make dizziness worse.Never support self-medication or recommend your medicine to another person. It is beneficial to have plenty of fluids while taking this medication. Before taking it, you should tell your doctor if you have any kidney and liver diseases. Pregnant or breastfeeding women should also consult their doctor before taking this medicine.",
    "howToUse": "Take this medicine in the dose and duration as advised by your doctor. 1 AL AX 5mg/75mg Capsule may be taken with or without food, but it is better to take it at a fixed time.",
    "safetyAdvise": "- Alcohol : UNSAFE <p> 1 AL AX 5mg/75mg Capsule may cause excessive drowsiness with alcohol. | - Pregnancy : CONSULT YOUR DOCTOR <p> 1 AL AX 5mg/75mg Capsule may be unsafe to use during pregnancy. Although there are limited studies in humans, animal studies have shown harmful effects on the developing baby. Your doctor will weigh the benefits and any potential risks before prescribing it to you. Please consult your doctor. | - Breast feeding : CONSULT YOUR DOCTOR <p> Information regarding the use of 1 AL AX 5mg/75mg Capsule during breastfeeding is not available. Please consult your doctor. | - Driving : UNSAFE <p> 1 AL AX 5mg/75mg Capsule may decrease alertness, affect your vision or make you feel sleepy and dizzy. Do not drive if these symptoms occur. | - Kidney : CAUTION <p> 1 AL AX 5mg/75mg Capsule should be used with caution in patients with kidney disease. Dose adjustment of 1 AL AX 5mg/75mg Capsule may be needed. Please consult your doctor.Use of 1 AL AX 5mg/75mg Capsule is not recommended in patients with severe kidney disease. | - Liver : CAUTION <p> 1 AL AX 5mg/75mg Capsule should be used with caution in patients with liver disease. Dose adjustment of 1 AL AX 5mg/75mg Capsule may be needed. Please consult your doctor.",
    "ifMiss": "If you miss a dose of 1 AL AX 5mg/75mg Capsule, take it as soon as possible. However, if it is almost time for your next dose, skip the missed dose and go back to your regular schedule. Do not double the dose.",
    "packagingDetail": "strip of 10 capsules",
    "package": "Strip",
    "qty": 10,
    "productForm": "Capsule",
    "mrp": 65,
    "prescriptionRequired": "Prescription Required",
    "factBox": "Habit Forming :: No|Therapeutic Class :: RESPIRATORY",
    "primaryUse": "Cough",
    "storage": "Store below 30°C",
    "useOf": "Cough",
    "commonSideEffect": "Upset stomach | Dryness in mouth | Headache | Fatigue | Sleepiness | Allergic reaction",
    "alcoholInteraction": "UNSAFE",
    "pregnancyInteraction": "CONSULT YOUR DOCTOR",
    "lactationInteraction": "CONSULT YOUR DOCTOR",
    "drivingInteraction": "UNSAFE",
    "kidneyInteraction": "CAUTION",
    "liverInteraction": "CAUTION",
    "manufacturerAddress": "B-8, MIDC Area, Waluj - 431 136, Dist. Aurangabad, Maharashtra",
    "countryOfOrigin": "India",
    "qa": "",
    "howItWorks": "1 AL AX 5mg/75mg Capsule is a combination of two medicines:.",
    "interaction": "",
    "manufacturerDetails": "",
    "marketerDetails": "FDC Ltd | B-8, MIDC Area, Waluj - 431 136, Dist. Aurangabad, Maharashtra",
    "expiration": "",
    "reference": "FDA approved prescribing information. Levocitrizine; 1995 [revised May 2007]. [Accessed 01 Apr. 2019] (online) Available from: <a> https://www.accessdata.fda.gov/drugsatfda_docs/label/2007/022064lbl.pdf | European Medicne Agency. Revised assessment report: Ambroxol and bromhexine containing medicinal products. 2015. [Accessed 01 Apr. 2019] (online) Available from: <a> https://www.ema.europa.eu/en/documents/referral/ambroxol-bromhexine-article-31-referral-prac-assessment-report_en.pdf | ScienceDirect. Ambroxol. [Accessed 01 Apr. 2019] (online) Available from: <a> https://www.sciencedirect.com/topics/neuroscience/ambroxol | Levocitrizine. Slough, Berkshire: UCB Pharma Limited; 2007 [revised 27 Mar. 2019]. [Accessed 01 Apr. 2019] (online) Available from: <a> https://www.medicines.org.uk/emc/product/348/smpc",
    "imageUrl": "",
    "id": "be02a9ee-531a-48cb-9c6b-64fe803a934b",
    "_ts": 1750550263
}

OrganizationMedicines


--------------------

{
    "organizationId": "9d9221e7-**************-3eb00fe96788",
    "medicineId": "1489b1e7-01f2-487a-9164-727278b05a46",
    "isActive": true,
    "price": 4567,
    "createdOn": "2025-08-15T07:11:06.857Z",
    "updatedOn": "2025-08-15T07:11:06.857Z",
    "created_on": "2025-08-15T07:11:06.857Z",
    "updated_on": "2025-08-15T07:11:06.857Z",
    "id": "e96ff2b2-ddcd-478f-aa24-a16c73db7810",
    "_rid": "gG1xAK8hIoPDIQAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xAK8hIoM=/docs/gG1xAK8hIoPDIQAAAAAAAA==/",
    "_etag": "\"000040c8-0000-0200-0000-689edd8a0000\"",
    "_attachments": "attachments/",
    "_ts": 1755241866
}

Organizations
------------
{
    "_rid": "gG1xAIDHudJjAAAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xAIDHudI=/docs/gG1xAIDHudJjAAAAAAAAAA==/",
    "_etag": "\"00006bc6-0000-0200-0000-689dc49e0000\"",
    "_attachments": "attachments/",
    "created_by": "",
    "updated_by": "",
    "created_on": "",
    "updated_on": "2025-08-14T11:12:30.109Z",
    "id": "3737a80a-f254-4a45-96c4-1710ed425a39",
    "name": "hasnakkp",
    "contactEmail": "<EMAIL>",
    "contactPersonName": "hasnaffs",
    "contactPhone": "9898989898",
    "address": {
        "street": "123 Main Street",
        "city": "New York",
        "state": "NY",
        "postalCode": "670603",
        "country": "India"
    },
    "description": "",
    "isActive": true,
    "registrationFee": 300,
    "createdAt": "2025-08-14T11:12:30.109Z",
    "updatedAt": "2025-08-14T11:12:30.109Z",
    "_ts": 1755169950
}

OrganizationTests
------------------
{
    "organizationId": "9d9221e7-**************-3eb00fe96788",
    "testId": "100150-2",
    "isActive": true,
    "price": 0,
    "departments": [],
    "createdOn": "2025-08-15T06:48:03.451Z",
    "updatedOn": "2025-08-15T07:07:52.634Z",
    "created_on": "2025-08-15T06:54:18.272Z",
    "updated_on": "2025-08-15T07:07:52.635Z",
    "id": "b4edf37b-bfde-4112-b78b-34df9bf37934",
    "_rid": "gG1xAPy86p0cPQcAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xAPy86p0=/docs/gG1xAPy86p0cPQcAAAAAAA==/",
    "_etag": "\"00001a9b-0000-0200-0000-689edcc80000\"",
    "_attachments": "attachments/",
    "_ts": 1755241672
}

packages
---------
{
    "_rid": "gG1xAIp3bwgCAAAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xAIp3bwg=/docs/gG1xAIp3bwgCAAAAAAAAAA==/",
    "_etag": "\"b60223a0-0000-0200-0000-680c87900000\"",
    "_attachments": "attachments/",
    "created_by": "",
    "updated_by": "",
    "created_on": "2025-04-26T05:05:31.236Z",
    "updated_on": "2025-04-26T07:13:19.317Z",
    "id": "983480e1-20bc-4c3a-8b6d-99b1e75bf5e7",
    "name": "Pain Relief Package",
    "type": "department",
    "medicines": [
        {
            "id": "83bc2540-a070-4c67-a020-8cb43b99256c",
            "DrugFormulation": "Tablet",
            "BrandName": "1 Nvp Tablet",
            "GenericName": "Doxylamine + Vitamin B6 + Folic Acid",
            "Strength": "20mg / 20mg / 5mg",
            "Measure": 10,
            "UnitOfMeasure": "Nos",
            "Cost": 120,
            "productId": "DRS000003"
        },
        {
            "id": "36726192-20ec-4444-8acc-acbd947241dd",
            "DrugFormulation": "Capsule",
            "BrandName": "1 AL AX 5mg/75mg Capsule",
            "GenericName": "Levocetirizine + Ambroxol",
            "Strength": "5mg / 75mg",
            "Measure": 10,
            "UnitOfMeasure": "Nos",
            "Cost": 65,
            "productId": "DRS000001"
        }
    ],
    "_ts": **********
}

PatientConsultations
-----------------
{
    "type": "InPatient",
    "doctor": "Dr-Shaji-Kurian",
    "date": "2024-07-22T05:41:00.000Z",
    "created_by": "0ec1d90f-f70e-4b86-abf3-1a88d9fe0a8a",
    "updated_by": "0ec1d90f-f70e-4b86-abf3-1a88d9fe0a8a",
    "patientId": "GX41994836",
    "created_on": "2024-07-22T04:26:35.498Z",
    "updated_on": "2024-07-22T04:26:35.498Z",
    "id": "962eecbb-1e92-4c99-8600-7841cd3ae1c1",
    "_rid": "gG1xAJoRtcUiAAAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xAJoRtcU=/docs/gG1xAJoRtcUiAAAAAAAAAA==/",
    "_etag": "\"0700600f-0000-0200-0000-669ddf7b0000\"",
    "_attachments": "attachments/",
    "_ts": **********
}

PatientDiagnosisNotes


-------------------
{
    "records": [
        {
            "record_id": "R1755180475124_0",
            "field": "Diagnosis",
            "content": "Fever",
            "doctor_id": "87f21423-8131-43e7-a198-76538992ea5c",
            "timestamp": "2025-08-14T14:07:55.124Z",
            "version": 2,
            "status": "active",
            "diagnosisStatus": "confirmed",
            "activityStatus": "inactive"
        },
        {
            "record_id": "R1746793211597",
            "field": "Diagnosis",
            "content": "<div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"d9a7b9ba-daf0-4d6f-8a5b-7ace3563fb99\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"d9a7b9ba-daf0-4d6f-8a5b-7ace3563fb99\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\">jk</p></div></div></div><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"8e70d9ed-00c2-4588-ae08-46d49b974a71\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"8e70d9ed-00c2-4588-ae08-46d49b974a71\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div></div></div></div>",
            "doctor_id": "627bf049-5d07-4303-8707-38af7db48197",
            "timestamp": "2025-05-09T12:20:11.597Z",
            "version": 1,
            "status": "active"
        }
    ],
    "create_by": "27bffdb0-8192-4d42-a908-82ec508eca8e",
    "update_by": "ee1c6e63-1a0b-4961-a1f0-13a96d163853",
    "patientId": "WV14737960",
    "created_on": "2025-05-09T12:20:12.117Z",
    "updated_on": "2025-08-14T14:08:15.584Z",
    "id": "f17aef9c-0dcc-4232-8436-2a8c8a1614de",
    "_rid": "gG1xAM0zNWswAAAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xAM0zNWs=/docs/gG1xAM0zNWswAAAAAAAAAA==/",
    "_etag": "\"7a005c0f-0000-0200-0000-689dedcf0000\"",
    "_attachments": "attachments/",
    "_ts": **********
}

PatientDemographics
--------------------
{
    "_rid": "gG1xAIOfFjIFAAAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xAIOfFjI=/docs/gG1xAIOfFjIFAAAAAAAAAA==/",
    "_etag": "\"2003dfd9-0000-0200-0000-689ad17c0000\"",
    "_attachments": "attachments/",
    "created_by": "",
    "updated_by": "",
    "created_on": "2025-08-01T04:29:32.165Z",
    "updated_on": "2025-08-12T05:30:36.931Z",
    "id": "e7221109-d7ae-45e4-a020-83f882eed17b",
    "name": "APARNA",
    "cmchId": "WL60945869",
    "dob": "1997-07-10",
    "age": "28 years",
    "sex": "Female",
    "maritalStatus": "Married",
    "contacts": [
        {
            "phone": "**********",
            "email": ""
        }
    ],
    "patientId": "WL60945869",
    "_ts": **********
}

PatientHistory
-----------------
{
    "summary": {
        "owner": {
            "id": "80fa2c02-96ae-4524-ab82-1f3832936928",
            "role": "admin",
            "name": "James samual ",
            "email": "<EMAIL>"
        },
        "vitals": {},
        "anthropometry": {},
        "generalphysicalexamination": {},
        "systemicexamination": {},
        "historyofpresenting": "<div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"3a240769-472e-4f58-9b07-c0f75e5ba620\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"3a240769-472e-4f58-9b07-c0f75e5ba620\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"07c5bc7a-9dee-4c55-945e-1a04569ba1d4\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"07c5bc7a-9dee-4c55-945e-1a04569ba1d4\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"7104fcce-88c0-4b5f-9ae5-5b310b72411c\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"7104fcce-88c0-4b5f-9ae5-5b310b72411c\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"ac221e04-b9f6-4d5f-81a0-bcf624eae281\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"ac221e04-b9f6-4d5f-81a0-bcf624eae281\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"e3f2625c-8129-4fd1-89d8-d4c111bb42cc\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"e3f2625c-8129-4fd1-89d8-d4c111bb42cc\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"18a581e2-bae2-4f33-89cf-3616aef61ddc\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"18a581e2-bae2-4f33-89cf-3616aef61ddc\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"a3087507-cbde-47c7-88e4-a9ac089f8c2a\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"a3087507-cbde-47c7-88e4-a9ac089f8c2a\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"9d7157c9-6cf9-4c62-b2b9-06d6235dde24\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"9d7157c9-6cf9-4c62-b2b9-06d6235dde24\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"fde33d4e-8372-4d67-a129-d3a9378954e0\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"fde33d4e-8372-4d67-a129-d3a9378954e0\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"bbc941ef-6374-4a5b-841c-68a1a1162ebe\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"bbc941ef-6374-4a5b-841c-68a1a1162ebe\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"408526d4-5b72-4102-a7f7-042a87e03c5d\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"408526d4-5b72-4102-a7f7-042a87e03c5d\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"47027eb4-16c7-4661-a0bc-ba3d569ede40\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"47027eb4-16c7-4661-a0bc-ba3d569ede40\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\">test</p></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"192a6bc3-7cb6-4039-94b5-6f44d70f3654\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"192a6bc3-7cb6-4039-94b5-6f44d70f3654\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"ddfc66e8-9cfe-46d0-b408-5543e40f8972\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"ddfc66e8-9cfe-46d0-b408-5543e40f8972\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"c8e20d51-ced3-4d48-b315-8ec1f75cd634\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"c8e20d51-ced3-4d48-b315-8ec1f75cd634\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"05141d4e-f312-4d88-a1cf-c9f11b83fdaf\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"05141d4e-f312-4d88-a1cf-c9f11b83fdaf\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"4aade27c-adfc-463a-bb8d-e365e09f007a\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"4aade27c-adfc-463a-bb8d-e365e09f007a\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"b01b2be3-8c35-4d36-9174-b5aaef4885f5\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"b01b2be3-8c35-4d36-9174-b5aaef4885f5\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"01e76cae-2f5e-4320-a608-792ab0c6a9c3\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"01e76cae-2f5e-4320-a608-792ab0c6a9c3\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"f6d4acd6-0b0a-4cbd-8d94-6ba35b885416\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"f6d4acd6-0b0a-4cbd-8d94-6ba35b885416\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"ce403de3-5ed7-4312-9333-f56e28aea095\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"ce403de3-5ed7-4312-9333-f56e28aea095\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"e199ff86-c7f3-4a74-875a-239aba3c3524\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"e199ff86-c7f3-4a74-875a-239aba3c3524\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"b4f74534-0bb2-4104-a4c7-a8a9e9060aab\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"b4f74534-0bb2-4104-a4c7-a8a9e9060aab\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"f84bee9b-1037-4ec5-979c-24c7a78fa880\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"f84bee9b-1037-4ec5-979c-24c7a78fa880\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\">test</p></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"446c15c5-2977-42af-989f-ef5f4f1d3405\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"446c15c5-2977-42af-989f-ef5f4f1d3405\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div></div></div></div>",
        "pastmedicalhistory": "<div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"35049afb-478e-4732-9802-7701d8472e17\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"35049afb-478e-4732-9802-7701d8472e17\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"a1efcbb3-cfb3-43e6-9813-bc466b32f1ea\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"a1efcbb3-cfb3-43e6-9813-bc466b32f1ea\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"bba3809f-e29a-4ee1-bd23-97620e1dd07e\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"bba3809f-e29a-4ee1-bd23-97620e1dd07e\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"190aa20a-f6c7-4c74-9709-868ac23c6132\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"190aa20a-f6c7-4c74-9709-868ac23c6132\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"69343c22-050c-44f1-a20f-904208b4edf2\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"69343c22-050c-44f1-a20f-904208b4edf2\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"12d2f728-0f6e-4f86-91ef-3091927b1d28\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"12d2f728-0f6e-4f86-91ef-3091927b1d28\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"fe7216e3-3c8c-4edb-bdb5-eb4535fef2ac\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"fe7216e3-3c8c-4edb-bdb5-eb4535fef2ac\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"f1a22933-a21e-428d-a6ee-555bb3fb112f\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"f1a22933-a21e-428d-a6ee-555bb3fb112f\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\">test</p></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"fd086d6f-cdc5-4fb2-935e-a88bec44db95\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"fd086d6f-cdc5-4fb2-935e-a88bec44db95\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div></div></div></div>",
        "pastsurgicalhistory": "<div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"a2de75dd-db6b-430d-9fd9-272293fa1b38\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"a2de75dd-db6b-430d-9fd9-272293fa1b38\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div></div></div></div>",
        "familyhistory": "<div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"2a6509ce-6fcb-4489-8fdd-4660657241ce\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"2a6509ce-6fcb-4489-8fdd-4660657241ce\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"e4a01778-73cc-4f50-bb44-ddeecc187a43\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"e4a01778-73cc-4f50-bb44-ddeecc187a43\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"8004af89-54a6-4275-9cdd-e1dfec9231f8\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"8004af89-54a6-4275-9cdd-e1dfec9231f8\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"8cc36460-d5b0-4d94-b6a7-dd2bf11867f6\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"8cc36460-d5b0-4d94-b6a7-dd2bf11867f6\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\">test</p></div></div></div></div></div></div></div></div></div></div></div></div><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"017af9f0-7502-46b8-a12f-501f48a73a21\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"017af9f0-7502-46b8-a12f-501f48a73a21\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div></div></div></div>",
        "addictionhistory": "<div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"317afa5e-66ff-4168-9fc8-4aa1cc2ba4c6\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"317afa5e-66ff-4168-9fc8-4aa1cc2ba4c6\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div></div></div></div>",
        "diethistory": "<div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"79d98049-773d-4b83-b2c4-87478caeb028\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"79d98049-773d-4b83-b2c4-87478caeb028\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div></div></div></div>",
        "physicalactivityhistory": "<div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"f3ceb1a4-ac9a-42cc-acbb-89cb1e1d1913\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"f3ceb1a4-ac9a-42cc-acbb-89cb1e1d1913\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div></div></div></div>",
        "stresshistory": "<div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"3b18ed36-4a4b-44f1-92d4-44dc495b0ba4\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"3b18ed36-4a4b-44f1-92d4-44dc495b0ba4\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"c394c3ed-5600-4c1d-a7ef-a4d6d6f8f1d2\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"c394c3ed-5600-4c1d-a7ef-a4d6d6f8f1d2\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\">test</p></div></div></div></div></div></div><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"3ec203bc-dee9-4c5d-bcea-12fabe2de593\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"3ec203bc-dee9-4c5d-bcea-12fabe2de593\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\">jkjkkjkjkj</p></div></div></div><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"7780509e-79e1-4197-9caa-5f083930d9e6\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"7780509e-79e1-4197-9caa-5f083930d9e6\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\">kkjkjkjk</p></div></div></div><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"f06c435b-ede3-4759-8f04-ad0c762101b3\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"f06c435b-ede3-4759-8f04-ad0c762101b3\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\">jkjkjkjkjkj</p></div></div></div><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"e08b499d-5c74-4c41-b15d-bb96ef60aefc\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"e08b499d-5c74-4c41-b15d-bb96ef60aefc\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div></div></div></div>",
        "sleephistory": "<div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"9380f264-09b2-489d-865b-af77a20467ec\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"9380f264-09b2-489d-865b-af77a20467ec\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div></div></div></div>",
        "currentmedicationhistory": "<div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"b7579776-367a-46ed-baa8-564b15199bb3\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"b7579776-367a-46ed-baa8-564b15199bb3\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"660618b9-8515-4cb1-9f3c-88047e13eaa8\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"660618b9-8515-4cb1-9f3c-88047e13eaa8\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"9c38b912-0a1b-4207-b3f5-199932fe7a7c\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"9c38b912-0a1b-4207-b3f5-199932fe7a7c\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"276713db-3422-412f-9387-d0baa45fdf08\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"276713db-3422-412f-9387-d0baa45fdf08\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"b2a96402-12fe-4674-bc9a-7458139d7c6b\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"b2a96402-12fe-4674-bc9a-7458139d7c6b\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"1c537aaf-a8ad-4b6e-86bd-54707a1a7256\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"1c537aaf-a8ad-4b6e-86bd-54707a1a7256\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"378b746f-6a6b-4503-a0e5-4df33cc66820\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"378b746f-6a6b-4503-a0e5-4df33cc66820\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\">fjdjfkdfkdkfjkjkjkjkjkjkj</p></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"fdb38a77-9674-4ceb-8f15-e42837c935f5\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"fdb38a77-9674-4ceb-8f15-e42837c935f5\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div></div></div></div>",
        "presentingcomplaints": "<div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"placeholder-block\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"placeholder-block\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"722a10b0-2c94-48e8-a755-3ca63c9e16fb\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"722a10b0-2c94-48e8-a755-3ca63c9e16fb\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"d598e142-3afb-4312-a83d-50de01f5231e\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"d598e142-3afb-4312-a83d-50de01f5231e\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"26a57881-3fa4-43d3-bff7-f199bd341963\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"26a57881-3fa4-43d3-bff7-f199bd341963\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div><div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"104fc1b3-2d04-4017-a7f7-8310f96c80f7\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"104fc1b3-2d04-4017-a7f7-8310f96c80f7\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\">test</p></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"a06088dc-ad5c-4b27-a637-20fdf8eab492\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"a06088dc-ad5c-4b27-a637-20fdf8eab492\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div></div></div></div>"
    },
    "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928",
    "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928",
    "status": "editable",
    "patientId": "YR75871604",
    "created_on": "2025-08-18T03:14:33.527Z",
    "updated_on": "2025-08-18T05:54:08.339Z",
    "id": "1153c281-a055-49a5-893c-a51b39b22ebd",
    "_rid": "gG1xAOcSqNfVAQAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xAOcSqNc=/docs/gG1xAOcSqNfVAQAAAAAAAA==/",
    "_etag": "\"4801b532-0000-0200-0000-68a2c0000000\"",
    "_attachments": "attachments/",
    "createdOn": "2025-08-18T03:14:33.527Z",
    "_ts": **********
}

PatientLabTests
---------------

{
    "patientId": "**********",
    "status": "Upload",
    "labTests": [
        {
            "testName": "B lymphocytes (Bld) [#/Vol]",
            "qty": 1,
            "instructions": "test",
            "cost": "0",
            "toBeDoneBy": "",
            "date": "2025-08-14",
            "testId": "11130-2",
            "results": "168.0",
            "reference": "<150",
            "status": "Ready",
            "fileMetadata": []
        },
        {
            "testName": "Oxygen content (Mid right atrium) [Moles/Vol]",
            "qty": 1,
            "instructions": "test\n",
            "cost": "0",
            "toBeDoneBy": "",
            "date": "2025-08-14",
            "testId": "10237-6",
            "results": "190.0",
            "reference": "<150",
            "status": "Ready",
            "fileMetadata": []
        },
        {
            "testName": "Fasciola sp Ab (S) [Titer]",
            "qty": 1,
            "instructions": "test",
            "cost": "0",
            "toBeDoneBy": "",
            "date": "2025-08-14",
            "testId": "25342-7",
            "results": "159.0",
            "reference": "<600",
            "status": "Ready",
            "fileMetadata": []
        },
        {
            "testName": "R wave dur L-V2",
            "qty": 1,
            "instructions": "test",
            "cost": "0",
            "toBeDoneBy": "",
            "date": "2025-08-14",
            "testId": "10005-7",
            "results": "145.0",
            "reference": "<200",
            "status": "Ready",
            "fileMetadata": []
        }
    ],
    "created_on": "2025-08-14T01:35:18.504Z",
    "updated_on": "2025-08-14T07:19:36.495Z",
    "id": "9e951ca4-70b5-407e-baa9-908d3235b56f",
    "_rid": "gG1xANE1Z6CLAAAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xANE1Z6A=/docs/gG1xANE1Z6CLAAAAAAAAAA==/",
    "_etag": "\"8a00e0a5-0000-0200-0000-689d8e080000\"",
    "_attachments": "attachments/",
    "_ts": **********
}

PatientLifeStyleNotes
-------------------------
{
    "author": "Daniel osei",
    "note": "<div class=\"bn-block-group\" data-node-type=\"blockGroup\"><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"2ab9de12-4930-4599-afe0-83306df6da5b\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"2ab9de12-4930-4599-afe0-83306df6da5b\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\">wemnlkmnowp;rekt</p></div></div></div><div class=\"bn-block-outer\" data-node-type=\"blockOuter\" data-id=\"02bb42a8-3d84-4cac-8082-fa3f57343556\"><div class=\"bn-block\" data-node-type=\"blockContainer\" data-id=\"02bb42a8-3d84-4cac-8082-fa3f57343556\"><div class=\"bn-block-content\" data-content-type=\"paragraph\"><p class=\"bn-inline-content\"></p></div></div></div></div>",
    "patientId": "WI11872097",
    "tags": [],
    "type": "lifestyleNote",
    "created_by": "ee1c6e63-1a0b-4961-a1f0-13a96d163853",
    "updated_by": "ee1c6e63-1a0b-4961-a1f0-13a96d163853",
    "id": "859cff66-bf7b-4b68-85f4-8a06038e6e09",
    "created_on": "2025-04-25T09:41:15.770Z",
    "updated_on": "2025-04-25T09:41:15.770Z",
    "_rid": "gG1xAMEpZXkkAAAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xAMEpZXk=/docs/gG1xAMEpZXkkAAAAAAAAAA==/",
    "_etag": "\"9102a8a9-0000-0200-0000-680b58bb0000\"",
    "_attachments": "attachments/",
    "_ts": **********
}

PatientProfiles
---------------
{
    "name": "ARYAN",
    "dob": "yyyy-12-Sa",
    "sex": "Male",
    "height": "",
    "weight": "",
    "maritalStatus": "",
    "address": {
        "city": "kannur",
        "state": "",
        "country": ""
    },
    "contact": {
        "phone": "**********",
        "email": ""
    },
    "proof": {
        "type": "",
        "url": ""
    },
    "insurance": {
        "provider": "",
        "url": ""
    },
    "id": "VJ18435861",
    "created_by": "ee1c6e63-1a0b-4961-a1f0-13a96d163853",
    "updated_by": "ee1c6e63-1a0b-4961-a1f0-13a96d163853",
    "organizationId": "569035f7-fd3f-44e7-85a3-579612590f35",
    "created_on": "2025-08-14T14:49:21.682Z",
    "updated_on": "2025-08-14T14:49:21.682Z",
    
    "_rid": "gG1xALTzRbZ9AQAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xALTzRbY=/docs/gG1xALTzRbZ9AQAAAAAAAA==/",
    "_etag": "\"7e00b237-0000-0200-0000-689df7710000\"",
    "_attachments": "attachments/",
    "_ts": **********
}

Payments
---------
{
    "_rid": "gG1xALg5O65pAAAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xALg5O64=/docs/gG1xALg5O65pAAAAAAAAAA==/",
    "_etag": "\"d20021f8-0000-0200-0000-68a2c0320000\"",
    "_attachments": "attachments/",
    "created_by": "",
    "updated_by": "",
    "created_on": "2025-08-18T05:54:58.943Z",
    "updated_on": "2025-08-18T05:54:58.943Z",
    "id": "452af676-dc46-4b6a-ac5a-e4e263feab04",
    "razorpayOrderId": "order_R6gnXAUXNSdwSW",
    "razorpayPaymentId": "",
    "razorpaySignature": "",
    "amount": 3153,
    "currency": "INR",
    "status": "created",
    "paymentType": "prescription",
    "patientId": "AR65897152",
    "organizationId": "3737a80a-f254-4a45-96c4-1710ed425a39",
    "description": "Prescription Medicines",
    "createdAt": "2025-08-18T05:54:57.328Z",
    "verifiedAt": null,
    "failureReason": null,
    "metadata": {
        "prescriptionId": "f9fceeac-190e-49d1-9b09-9499668389a3"
    },
    "notes": {},
    "_ts": **********
}

prescriptions
----------------
{
    "_rid": "gG1xAMNykisjAQAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xAMNykis=/docs/gG1xAMNykisjAQAAAAAAAA==/",
    "_etag": "\"9d006df3-0000-0200-0000-68a2e4c00000\"",
    "_attachments": "attachments/",
    "created_by": "",
    "updated_by": "",
    "created_on": "2025-08-18T08:30:56.198Z",
    "updated_on": "2025-08-18T08:30:56.198Z",
    "id": "33431c5d-1c06-4608-8081-85a78cd5bc01",
    "patientId": "UH15717170",
    "doctor": "Sajidha Abdulrahman",
    "doctorEmail": "<EMAIL>",
    "medicines": [
        {
            "id": "be02a9ee-531a-48cb-9c6b-64fe803a934b",
            "drugForm": "CAP",
            "genericName": "1 AL AX 5mg/75mg Capsule",
            "brandName": "1 AL AX 5mg/75mg Capsule",
            "strength": 10,
            "measure": 10,
            "uom": "Nos",
            "unit": "",
            "frequency": "qd",
            "duration": "3D",
            "durationType": "",
            "quantity": "100",
            "route": "TOP",
            "instructions": "efe",
            "cost": "6500.00",
            "canSubstitute": false
        }
    ],
    "status": "Paid",
    "_ts": **********
}
Queues
---------------
{
    "id": "QUEUE20250818447",
    "appointmentId": "APM20250818239",
    "patientId": "YR75871604",
    "time": "1:40 PM",
    "status": "Booked-Booked",
    "queuePosition": 7,
    "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928",
    "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928",
    "type": "returning",
    "doctorId": "dcc0c06f-5f75-4007-9966-315282309998",
    "date": "2025-08-18T08:10:00.000Z",
    "department": "InPatient",
    "created_on": "2025-08-18T08:38:41.468Z",
    "updated_on": "2025-08-18T08:38:41.468Z",
    "_rid": "gG1xAIztKCHgAwAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xAIztKCE=/docs/gG1xAIztKCHgAwAAAAAAAA==/",
    "_etag": "\"5601b182-0000-0200-0000-68a2e6910000\"",
    "_attachments": "attachments/",
    "_ts": **********
}

roles
---------
{
    "id": "07fe2cb7-536c-467b-97c4-f4d206386565-ORGANIZATION_SUPER_ADMIN",
    "name": "organization Super Admin",
    "organizationId": "07fe2cb7-536c-467b-97c4-f4d206386565",
    "isDefault": true,
    "created_on": "2025-08-12T08:27:39.511Z",
    "updated_on": "2025-08-12T08:27:39.511Z",
    "_rid": "gG1xAJGe7EDKAQAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xAJGe7EA=/docs/gG1xAJGe7EDKAQAAAAAAAA==/",
    "_etag": "\"ae07a18a-0000-0200-0000-689afafb0000\"",
    "_attachments": "attachments/",
    "_ts": **********
}

{
    "id": "9d2f0eaa-3f7d-49bd-ae07-36bf82aedcb9",
    "roleName": "senior doctor 2",
    "organizationId": "9d9221e7-**************-3eb00fe96788",
    "APIs": [
        {
            "api": "lab-tests",
            "methods": [
                "GET"
            ],
            "permissionKey": "emr.lab-test.view"
        },
        {
            "api": "patient-lab-test/details",
            "methods": [
                "GET"
            ],
            "permissionKey": "emr.lab-test.view"
        },
        {
            "api": "lab-tests",
            "methods": [
                "POST",
                "PATCH",
                "DELETE"
            ],
            "permissionKey": "emr.lab-test.manage"
        },
        {
            "api": "patient-lab-test",
            "methods": [
                "POST",
                "PATCH",
                "DELETE"
            ],
            "permissionKey": "emr.lab-test.manage"
        },
        {
            "api": "patient-lab-test/search",
            "methods": [
                "POST",
                "PATCH",
                "DELETE"
            ],
            "permissionKey": "emr.lab-test.manage"
        },
        {
            "api": "lab-tests/search",
            "methods": [
                "POST"
            ],
            "permissionKey": "emr.lab-test.search"
        },
        {
            "api": "patient-lab-test/search",
            "methods": [
                "POST"
            ],
            "permissionKey": "emr.lab-test.search"
        },
        {
            "api": "package/tests",
            "methods": [
                "GET"
            ],
            "permissionKey": "emr.test-package.view"
        },
        {
            "api": "packages/user-specific",
            "methods": [
                "GET"
            ],
            "permissionKey": "emr.test-package.view"
        },
        {
            "api": "prescription-package",
            "methods": [
                "GET"
            ],
            "permissionKey": "emr.prescription-package.view"
        },
        {
            "api": "prescription-package/medicines",
            "methods": [
                "GET"
            ],
            "permissionKey": "emr.prescription-package.view"
        },
        {
            "api": "list-roles",
            "methods": [
                "GET",
                "POST",
                "PATCH",
                "DELETE"
            ],
            "permissionKey": "role.manage"
        },
        {
            "api": "role",
            "methods": [
                "GET",
                "POST",
                "PATCH",
                "DELETE"
            ],
            "permissionKey": "role.manage"
        },
        {
            "api": "permissions/api-list",
            "methods": [
                "GET",
                "POST"
            ],
            "permissionKey": "permission.manage"
        },
        {
            "api": "assign-permissions",
            "methods": [
                "GET",
                "POST"
            ],
            "permissionKey": "permission.manage"
        },
        {
            "api": "list-organizations",
            "methods": [
                "GET",
                "POST",
                "PATCH",
                "DELETE"
            ],
            "permissionKey": "organization.manage"
        },
        {
            "api": "organization",
            "methods": [
                "GET",
                "POST",
                "PATCH",
                "DELETE"
            ],
            "permissionKey": "organization.manage"
        },
        {
            "api": "organization/patients",
            "methods": [
                "GET"
            ],
            "permissionKey": "organization.patients.view"
        },
        {
            "api": "dashboard/summary",
            "methods": [
                "GET"
            ],
            "permissionKey": "dashboard.view"
        },
        {
            "api": "user",
            "methods": [
                "GET"
            ],
            "permissionKey": "user.view"
        },
        {
            "api": "user/list",
            "methods": [
                "GET"
            ],
            "permissionKey": "user.view"
        },
        {
            "api": "user",
            "methods": [
                "POST",
                "PATCH"
            ],
            "permissionKey": "user.manage"
        },
        {
            "api": "usersignup",
            "methods": [
                "POST",
                "PATCH"
            ],
            "permissionKey": "user.manage"
        },
        {
            "api": "patient/lifestyle",
            "methods": [
                "GET",
                "POST",
                "PATCH",
                "DELETE"
            ],
            "permissionKey": "emr.lifestyle.manage"
        },
        {
            "api": "lifestyle/question",
            "methods": [
                "GET",
                "POST",
                "PATCH",
                "DELETE"
            ],
            "permissionKey": "emr.lifestyle.manage"
        },
        {
            "permissionKey": "emr.access"
        },
        {
            "api": "patient/consulting",
            "methods": [
                "GET",
                "POST",
                "PUT"
            ],
            "permissionKey": "emr.consultation.manage"
        },
        {
            "api": "doctor",
            "methods": [
                "GET"
            ],
            "permissionKey": "emr.doctorprofile.view"
        },
        {
            "api": "doctor",
            "methods": [
                "POST",
                "PUT",
                "PATCH"
            ],
            "permissionKey": "emr.doctorprofile.edit"
        },
        {
            "api": "lab-report/upload",
            "methods": [
                "POST",
                "GET"
            ],
            "permissionKey": "emr.reports.manage"
        },
        {
            "api": "lab-report/preview",
            "methods": [
                "POST",
                "GET"
            ],
            "permissionKey": "emr.reports.manage"
        },
        {
            "api": "package/add-tests",
            "methods": [
                "POST"
            ],
            "permissionKey": "emr.test-package.manage"
        },
        {
            "api": "package/remove-test",
            "methods": [
                "POST"
            ],
            "permissionKey": "emr.test-package.manage"
        },
        {
            "permissionKey": "mrd.access"
        },
        {
            "api": "patient/history",
            "methods": [
                "GET"
            ],
            "permissionKey": "mrd.manage-patient.view"
        },
        {
            "api": "patient/vitals",
            "methods": [
                "GET"
            ],
            "permissionKey": "mrd.manage-patient.view"
        },
        {
            "api": "patient/history",
            "methods": [
                "POST",
                "PUT",
                "PATCH"
            ],
            "permissionKey": "mrd.manage-patient.edit"
        },
        {
            "api": "patient/vitals",
            "methods": [
                "POST",
                "PUT",
                "PATCH"
            ],
            "permissionKey": "mrd.manage-patient.edit"
        },
        {
            "api": "queue",
            "methods": [
                "GET",
                "POST",
                "PATCH"
            ],
            "permissionKey": "mrd.patient-queue.manage"
        },
        {
            "api": "appointment/queue",
            "methods": [
                "GET",
                "POST",
                "PATCH"
            ],
            "permissionKey": "mrd.patient-queue.manage"
        },
        {
            "api": "patient",
            "methods": [
                "GET"
            ],
            "permissionKey": "emr.patientinfo.view"
        },
        {
            "api": "patient",
            "methods": [
                "POST",
                "PUT",
                "PATCH"
            ],
            "permissionKey": "emr.patientinfo.edit"
        },
        {
            "api": "prescriptions",
            "methods": [
                "GET"
            ],
            "permissionKey": "emr.prescription.view"
        },
        {
            "api": "prescriptions/details",
            "methods": [
                "GET"
            ],
            "permissionKey": "emr.prescription.view"
        },
        {
            "api": "prescriptions",
            "methods": [
                "POST",
                "PATCH"
            ],
            "permissionKey": "emr.prescription.manage"
        },
        {
            "api": "prescriptions/search",
            "methods": [
                "POST",
                "PATCH"
            ],
            "permissionKey": "emr.prescription.manage"
        },
        {
            "api": "package",
            "methods": [
                "POST"
            ],
            "permissionKey": "emr.medicine-package.manage"
        },
        {
            "api": "package/add-medicines",
            "methods": [
                "POST"
            ],
            "permissionKey": "emr.medicine-package.manage"
        },
        {
            "api": "package/remove-medicine",
            "methods": [
                "POST"
            ],
            "permissionKey": "emr.medicine-package.manage"
        }
    ],
    "created_on": "2025-08-12T10:03:56.922Z",
    "updated_on": "2025-08-12T10:27:01.287Z",
    "_rid": "gG1xANXDCL4FAQAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xANXDCL4=/docs/gG1xANXDCL4FAQAAAAAAAA==/",
    "_etag": "\"0a00741c-0000-0200-0000-689b16f50000\"",
    "_attachments": "attachments/",
    "_ts": 1754994421
}

test-packages
-----------------------
{
    "_rid": "gG1xAIDlbgM1AAAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xAIDlbgM=/docs/gG1xAIDlbgM1AAAAAAAAAA==/",
    "_etag": "\"0400692b-0000-0200-0000-689c2b120000\"",
    "_attachments": "attachments/",
    "created_by": "",
    "updated_by": "",
    "created_on": "2025-08-13T06:05:06.092Z",
    "updated_on": "2025-08-13T06:05:06.092Z",
    "id": "60461ff2-75dd-4da8-8772-498108109017",
    "name": "Package test",
    "type": "user",
    "tests": [
        {
            "testId": "57033-3",
            "testName": "A. alternata Ab Immune diff Ql (S)"
        }
    ],
    "userId": "80fa2c02-96ae-4524-ab82-1f3832936928",
    "_ts": 1755065106
}

Users
---------
{
    "userRole": "doctor",
    "userType": "doctor",
    "name": "Thanu",
    "email": "<EMAIL>",
    "organizationId": "5f65b94e-f664-41d3-bd15-d795980f2135",
    "roleId": "5f65b94e-f664-41d3-bd15-d795980f2135-DOCTOR",
    "organizationName": null,
    "consultationFee": 300,
    "created_by": "cdf7f72c-7584-4eed-8ce9-d4f704d3a4e7",
    "updated_by": "cdf7f72c-7584-4eed-8ce9-d4f704d3a4e7",
    "isActive": true,
    "isOrganizationMainAdmin": false,
    "b2cUserId": "93073981-ba41-47ff-8cd1-08d770cdb304",
    "created_on": "2025-08-18T05:20:07.975Z",
    "updated_on": "2025-08-18T05:20:07.975Z",
    "id": "9f39c9ca-aa2f-42df-ab99-77f9d5d46ccd",
    "_rid": "gG1xAOc2H1qTAQAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xAOc2H1o=/docs/gG1xAOc2H1qTAQAAAAAAAA==/",
    "_etag": "\"4601b71e-0000-0200-0000-68a2b8080000\"",
    "_attachments": "attachments/",
    "_ts": **********
}

Vitals
----------
{
    "vitals": {
        "heartRate": 2,
        "respiratoryRate": 44,
        "spO2": "45",
        "temperature": "17",
        "height": "190",
        "weight": "73",
        "bmi": "20.22",
        "waistCircumference": "33",
        "bloodPressure": "33/33",
        "pulse": "50",
        "sbp": "80",
        "dbp": "50",
        "rr": "15"
    },
    "id": "b68f22cc-0708-47ee-811d-337ee878fc71",
    "patientId": "WL60945869",
    "created_by": "ccca0374-a1ba-4e58-8be7-eb3c3af5a638",
    "update_by": "ccca0374-a1ba-4e58-8be7-eb3c3af5a638",
    "created_on": "2025-08-15T03:37:59.734Z",
    "updated_on": "2025-08-15T03:37:59.734Z",
    "_rid": "gG1xAJDmaXLdAQAAAAAAAA==",
    "_self": "dbs/gG1xAA==/colls/gG1xAJDmaXI=/docs/gG1xAJDmaXLdAQAAAAAAAA==/",
    "_etag": "\"ae00d789-0000-0200-0000-689eab970000\"",
    "_attachments": "attachments/",
    "_ts": **********
}


